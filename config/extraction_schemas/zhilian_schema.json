{"name": "Zhilian Jobs Extraction Schema", "description": "智联招聘职位信息提取模式", "baseSelector": ".newlist_list_content table, .zwmc, .joblist-item, .positionlist-item", "fields": [{"name": "title", "selector": ".zwmc a, .job-title a, .position-title a, .jobname a", "type": "text", "required": true}, {"name": "company", "selector": ".gsmc a, .company-name a, .company a, .companyname a", "type": "text", "required": true}, {"name": "location", "selector": ".gzdd, .job-location, .workarea, .location", "type": "text", "required": true}, {"name": "salary", "selector": ".zwyx, .job-salary, .salary, .money", "type": "text", "required": true}, {"name": "experience", "selector": ".gzjy, .job-experience, .experience, .require", "type": "text", "required": false}, {"name": "education", "selector": ".xueli, .job-education, .education, .require", "type": "text", "required": false}, {"name": "job_url", "selector": ".zwmc a, .job-title a, .position-title a, .jobname a", "type": "attribute", "attribute": "href", "required": false}, {"name": "company_url", "selector": ".gsmc a, .company-name a, .company a, .companyname a", "type": "attribute", "attribute": "href", "required": false}, {"name": "publish_time", "selector": ".gxsj, .job-time, .time, .updatetime", "type": "text", "required": false}, {"name": "job_type", "selector": ".job-type, .type, .jobtype", "type": "text", "required": false}, {"name": "company_type", "selector": ".company-type, .ctype, .companytype", "type": "text", "required": false}, {"name": "company_size", "selector": ".company-size, .csize, .companysize", "type": "text", "required": false}, {"name": "benefits", "selector": ".job-benefit, .welfare, .benefits", "type": "text", "required": false}], "pagination": {"next_page_selector": ".pager .next, .pagination .next", "page_number_selector": ".pager .current, .pagination .current"}, "filters": {"min_title_length": 2, "min_company_length": 2, "required_fields": ["title", "company", "location", "salary"], "exclude_keywords": ["兼职", "小时工", "临时工", "代理", "刷单", "打字员"]}}