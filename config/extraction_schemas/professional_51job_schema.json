{"name": "Professional 51job Jobs Extraction Schema", "description": "专业前程无忧职位信息提取模式", "baseSelector": ".professional-51job-job", "fields": [{"name": "title", "selector": ".professional-51job-title", "type": "text", "required": true}, {"name": "company", "selector": ".professional-51job-company", "type": "text", "required": true}, {"name": "location", "selector": ".professional-51job-location", "type": "text", "required": true}, {"name": "salary", "selector": ".professional-51job-salary", "type": "text", "required": true}], "pagination": {"next_page_selector": ".professional-51job-next", "page_number_selector": ".professional-51job-current"}, "filters": {"min_title_length": 2, "min_company_length": 2, "required_fields": ["title", "company", "location", "salary"]}}