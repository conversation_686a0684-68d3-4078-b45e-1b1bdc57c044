# 工业职位需求爬虫配置文件
# Industrial Position Demand Crawler Configuration

# 爬虫基础设置
crawler:
  max_concurrent_requests: 3      # 最大并发请求数
  request_delay: 2.0             # 请求间隔(秒)
  timeout: 30                    # 请求超时时间(秒)
  retry_attempts: 3              # 重试次数
  headless: true                 # 是否使用无头浏览器

# 反反爬虫设置
anti_detection:
  rotate_user_agents: true       # 是否轮换用户代理
  use_proxy: false              # 是否使用代理
  proxy_list: []                # 代理列表
  random_delay_range: [1, 3]    # 随机延迟范围(秒)

# 数据存储设置
data_storage:
  output_format: "json"         # 输出格式: json, csv, database
  output_directory: "data/output"  # 输出目录
  database_url: "sqlite:///data/jobs.db"  # 数据库连接URL
  enable_deduplication: true    # 是否启用去重

# 网站配置
websites:
  # 真实数据爬虫 (多源真实数据获取)
  real:
    base_url: "real://"
    search_url_template: "real://search?keyword={keyword}"
    enabled: true
    extraction_schema_path: "config/extraction_schemas/real_schema.json"

  # 演示爬虫 (生成模拟数据用于测试)
  demo:
    base_url: "demo://"
    search_url_template: "demo://search?keyword={keyword}"
    enabled: false  # 禁用演示爬虫，优先使用真实数据
    extraction_schema_path: "config/extraction_schemas/demo_schema.json"

  # 拉勾网 (相对容易爬取)
  lagou:
    base_url: "https://www.lagou.com"
    search_url_template: "https://www.lagou.com/jobs/list_{keyword}"
    enabled: false  # 暂时禁用，需要特殊处理
    extraction_schema_path: "config/extraction_schemas/lagou_schema.json"

  # Boss直聘 (更现代，反爬虫相对较弱)
  boss:
    base_url: "https://www.zhipin.com"
    search_url_template: "https://www.zhipin.com/web/geek/job?query={keyword}&city=101010100"
    enabled: false  # 暂时禁用，需要特殊处理
    extraction_schema_path: "config/extraction_schemas/boss_schema.json"

  # 智联招聘
  zhilian:
    base_url: "https://www.zhaopin.com"
    search_url_template: "https://sou.zhaopin.com/jobs/searchresult.ashx?kw={keyword}&sm=0&p=1"
    enabled: false  # 暂时禁用，需要特殊处理
    extraction_schema_path: "config/extraction_schemas/zhilian_schema.json"

  # 猎聘网
  liepin:
    base_url: "https://www.liepin.com"
    search_url_template: "https://www.liepin.com/zhaopin/?key={keyword}"
    enabled: false  # 暂时禁用，反爬虫较强
    extraction_schema_path: "config/extraction_schemas/liepin_schema.json"

  # 前程无忧
  job51:
    base_url: "https://www.51job.com"
    search_url_template: "https://search.51job.com/list/000000,000000,0000,00,9,99,{keyword},2,1.html"
    enabled: false  # 暂时禁用，需要特殊处理
    extraction_schema_path: "config/extraction_schemas/job51_schema.json"

  # 58同城
  job58:
    base_url: "https://www.58.com"
    search_url_template: "https://www.58.com/sou/?key={keyword}"
    enabled: false  # 暂时禁用
    extraction_schema_path: "config/extraction_schemas/job58_schema.json"

# 爬取任务设置
crawl_settings:
  max_pages_per_site: 5         # 每个网站最大爬取页数
  enable_detail_crawl: false    # 是否爬取职位详情页
  concurrent_sites: 1          # 并发爬取的网站数量
  
# 数据过滤设置
data_filter:
  min_salary: 0                # 最低薪资要求(K)
  max_salary: 999              # 最高薪资要求(K)
  required_keywords:           # 必须包含的关键词
    - "计算机"
    - "软件"
    - "程序"
    - "开发"
    - "技术"
  excluded_keywords:           # 排除的关键词
    - "销售"
    - "客服"
    - "行政"
