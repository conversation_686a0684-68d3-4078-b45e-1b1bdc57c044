#!/usr/bin/env python3
"""
交互式爬虫启动器
Interactive crawler launcher
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.main import CrawlerManager
from src.utils import logger, config


def print_banner():
    """打印欢迎横幅"""
    print("\n" + "="*70)
    print("🚀 工业职位需求爬虫系统 - 交互式模式")
    print("   Industrial Position Demand Crawler - Interactive Mode")
    print("="*70)


def get_search_keyword():
    """获取搜索关键词"""
    print("\n📝 请输入您要搜索的职位关键词:")
    print("💡 建议输入:")
    print("   • 专业名称: 计算机科学与技术、软件工程、信息安全")
    print("   • 技能关键词: Python、Java、前端、后端、算法")
    print("   • 职位名称: 开发工程师、数据分析师、产品经理")
    print("   • 行业领域: 人工智能、大数据、云计算、区块链")
    
    while True:
        keyword = input("\n🔍 请输入关键词: ").strip()
        
        if not keyword:
            print("❌ 关键词不能为空，请重新输入")
            continue
        elif len(keyword) < 2:
            print("❌ 关键词太短，请输入至少2个字符")
            continue
        elif len(keyword) > 50:
            print("❌ 关键词太长，请输入不超过50个字符")
            continue
        else:
            print(f"✅ 已设置关键词: {keyword}")
            return keyword


def select_websites():
    """选择要爬取的网站"""
    print("\n🌐 请选择要爬取的网站:")
    
    websites = {
        '1': ('liepin', '猎聘网'),
        '2': ('job51', '前程无忧'),
        '3': ('job58', '58同城'),
        '4': ('all', '所有网站')
    }
    
    for key, (code, name) in websites.items():
        enabled = "✅" if code == 'all' or config.websites.get(code, {}).enabled else "❌"
        print(f"   {key}. {name} {enabled}")
    
    while True:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice in websites:
            code, name = websites[choice]
            if code == 'all':
                selected = [name for name, site in config.websites.items() if site.enabled]
                print(f"✅ 已选择: 所有启用的网站 ({', '.join(selected)})")
                return None  # None表示所有网站
            else:
                print(f"✅ 已选择: {name}")
                return [code]
        else:
            print("❌ 无效选择，请输入1-4")


def get_crawl_settings():
    """获取爬取设置"""
    print("\n⚙️ 爬取设置:")
    
    # 页数设置
    while True:
        try:
            pages_input = input("📄 每个网站爬取页数 (默认5页): ").strip()
            if not pages_input:
                pages = 5
                break
            pages = int(pages_input)
            if pages < 1:
                print("❌ 页数必须大于0")
                continue
            elif pages > 20:
                print("⚠️ 页数较多，建议不超过20页")
                confirm = input("是否继续? (y/N): ").lower().startswith('y')
                if not confirm:
                    continue
            break
        except ValueError:
            print("❌ 请输入有效的数字")
    
    # 输出格式
    print("\n💾 选择输出格式:")
    formats = {
        '1': ('json', 'JSON格式'),
        '2': ('csv', 'CSV格式'),
        '3': ('database', '数据库存储')
    }
    
    for key, (code, name) in formats.items():
        print(f"   {key}. {name}")
    
    while True:
        choice = input("请选择输出格式 (1-3, 默认JSON): ").strip()
        if not choice:
            output_format = 'json'
            break
        elif choice in formats:
            output_format = formats[choice][0]
            break
        else:
            print("❌ 无效选择，请输入1-3")
    
    print(f"✅ 爬取设置: {pages}页/网站, 输出格式: {output_format}")
    
    return pages, output_format


def show_preview(keyword, sites, pages, output_format):
    """显示爬取预览"""
    print("\n" + "="*50)
    print("📋 爬取预览")
    print("="*50)
    print(f"🔍 搜索关键词: {keyword}")
    
    if sites:
        site_names = [config.websites[site].name if site in config.websites else site for site in sites]
        print(f"🌐 目标网站: {', '.join(site_names)}")
    else:
        enabled_sites = [site.name for site in config.get_enabled_websites()]
        print(f"🌐 目标网站: {', '.join(enabled_sites)}")
    
    print(f"📄 爬取页数: {pages}页/网站")
    print(f"💾 输出格式: {output_format}")
    
    print("\n🔗 生成的搜索URL:")
    for site_name, site_config in config.websites.items():
        if sites and site_name not in sites:
            continue
        if not site_config.enabled:
            continue
        
        search_url = site_config.get_search_url(keyword)
        site_display_name = {
            'liepin': '猎聘网',
            'job51': '前程无忧', 
            'job58': '58同城'
        }.get(site_name, site_name)
        
        print(f"   {site_display_name}: {search_url}")
    
    print("="*50)
    
    # 确认开始
    while True:
        confirm = input("\n🚀 是否开始爬取? (y/N): ").strip().lower()
        if confirm.startswith('y'):
            return True
        elif confirm.startswith('n') or not confirm:
            print("❌ 已取消爬取")
            return False
        else:
            print("请输入 y 或 n")


async def run_interactive_crawler():
    """运行交互式爬虫"""
    try:
        # 获取用户输入
        keyword = get_search_keyword()
        sites = select_websites()
        pages, output_format = get_crawl_settings()
        
        # 显示预览并确认
        if not show_preview(keyword, sites, pages, output_format):
            return
        
        # 创建必要目录
        Path("data/output").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
        
        # 开始爬取
        print("\n🚀 开始爬取...")
        logger.info(f"交互式爬取开始，关键词: {keyword}")
        
        crawler_manager = CrawlerManager(keyword)
        
        if sites:
            # 爬取指定网站
            all_jobs = []
            for site_name in sites:
                if site_name in crawler_manager.crawlers:
                    print(f"\n📡 正在爬取 {site_name}...")
                    jobs = await crawler_manager.crawl_single_site(site_name, pages)
                    all_jobs.extend(jobs)
                else:
                    print(f"⚠️ 网站 {site_name} 未启用或不存在")
        else:
            # 爬取所有网站
            all_jobs = await crawler_manager.crawl_all_sites(pages)
        
        # 保存数据
        if all_jobs:
            print(f"\n💾 保存数据...")
            filepath = crawler_manager.save_jobs(all_jobs, output_format)
            
            # 同时保存JSON格式
            if output_format != "json":
                json_path = crawler_manager.save_jobs(all_jobs, "json")
                print(f"📄 JSON文件: {json_path}")
            
            # 导出Excel
            try:
                excel_path = crawler_manager.data_storage.export_to_excel(all_jobs)
                print(f"📊 Excel文件: {excel_path}")
            except Exception as e:
                print(f"⚠️ Excel导出失败: {e}")
            
            print(f"✅ 主要数据文件: {filepath}")
        
        # 显示统计
        crawler_manager.print_statistics()
        
        print("\n🎉 爬取完成！")
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断爬取")
    except Exception as e:
        print(f"\n❌ 爬取过程发生错误: {e}")
        logger.error(f"交互式爬取失败: {e}")


def main():
    """主函数"""
    print_banner()
    
    print("\n📋 系统检查...")
    
    # 检查配置
    try:
        enabled_sites = config.get_enabled_websites()
        print(f"✅ 配置加载成功，启用网站: {len(enabled_sites)} 个")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 运行交互式爬虫
    asyncio.run(run_interactive_crawler())


if __name__ == "__main__":
    main()
