[{"title": "Python数据工程师", "company": "搜狐", "location": "重庆-高新区", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉Spark, ETL, Hadoop; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 免费班车, 团建活动, 通讯补贴, 技术津贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.268862", "job_id": "df9d1232d4ff16b3"}, {"title": "高级Java开发工程师", "company": "字节跳动", "location": "郑州-高新区", "salary": "25K-45K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉微服务, MySQL, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 五险一金, 免费班车, 年终奖, 股票期权, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.269137", "job_id": "af615c5f887ba0c8"}, {"title": "Python数据工程师", "company": "滴滴出行", "location": "深圳-宝安区", "salary": "25K-40K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Hadoop, Spark, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 免费班车, 健身房, 股票期权, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.269329", "job_id": "0d010c3a5a87467b"}, {"title": "Python数据工程师", "company": "百度", "location": "天津-经济开发区", "salary": "18K-30K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉ETL, 数据仓库, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 五险一金, 下午茶, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.269504", "job_id": "caa9909e9129f0aa"}, {"title": "Python数据工程师", "company": "新浪", "location": "昆明-经济开发区", "salary": "25K-40K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉ETL, Python, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 股票期权, 补充医疗保险, 交通补贴, 五险一金, 免费班车, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.269679", "job_id": "a10eec222dc7b5fb"}, {"title": "高级Java开发工程师", "company": "字节跳动", "location": "深圳-龙岗区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Spring Boot, Java, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 下午茶, 交通补贴, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.269865", "job_id": "ebc781c3f82aa555"}, {"title": "Python数据工程师", "company": "搜狐", "location": "昆明-高新区", "salary": "25K-40K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉数据仓库, ETL, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 补充医疗保险, 带薪年假, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.270037", "job_id": "4a483f11a8978d95"}, {"title": "Python数据工程师", "company": "联想集团", "location": "青岛-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Spark, ETL, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 年终奖, 通讯补贴, 培训机会, 技术津贴, 交通补贴, 补充医疗保险, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.270203", "job_id": "5367bfd7dc04eb44"}, {"title": "高级Java开发工程师", "company": "招商银行", "location": "济南-经济开发区", "salary": "30K-50K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉MySQL, Redis, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 年终奖, 补充医疗保险, 下午茶, 带薪年假, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.270368", "job_id": "8ab0089a0dbd8627"}, {"title": "高级Java开发工程师", "company": "联想集团", "location": "大连-高新区", "salary": "25K-45K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Redis, MySQL, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 健身房, 带薪年假, 团建活动, 年终奖, 培训机会, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.270542", "job_id": "62b777e50f7eaff1"}, {"title": "Python数据工程师", "company": "新浪", "location": "大连-高新区", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据仓库, <PERSON><PERSON>, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 股票期权, 弹性工作, 年终奖, 远程办公, 补充医疗保险, 五险一金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.270734", "job_id": "bdb11fa4f6fdf798"}, {"title": "高级Java开发工程师", "company": "360", "location": "深圳-宝安区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉微服务, MySQL, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 远程办公, 通讯补贴, 弹性工作, 绩效奖金, 带薪年假, 团建活动, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.270915", "job_id": "4b0ebc472b1ede88"}, {"title": "高级Java开发工程师", "company": "搜狐", "location": "昆明-经济开发区", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Java, Kafka, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 免费班车, 交通补贴, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.271092", "job_id": "54ef9e76530503fe"}, {"title": "Python数据工程师", "company": "招商银行", "location": "南京-经济开发区", "salary": "25K-40K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Python, 数据仓库, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 交通补贴, 免费午餐, 弹性工作, 远程办公, 股票期权, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.271276", "job_id": "0894b56b28ed703a"}, {"title": "高级Java开发工程师", "company": "中兴通讯", "location": "福州-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉MySQL, Spring Boot, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 通讯补贴, 年终奖, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.271442", "job_id": "fdab34f08aa77fe9"}, {"title": "Python数据工程师", "company": "新浪", "location": "重庆-市中心", "salary": "18K-30K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, Python, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 免费班车, 培训机会, 下午茶, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.271607", "job_id": "fd10208dd3d05a23"}, {"title": "Python数据工程师", "company": "小米", "location": "西安-高新区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 技术津贴, 下午茶, 带薪年假, 年终奖, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.271767", "job_id": "05b1c40ede74fdb2"}, {"title": "Python数据工程师", "company": "工商银行", "location": "苏州-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Spark, <PERSON><PERSON>, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 通讯补贴, 培训机会, 住房补贴, 绩效奖金, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.271929", "job_id": "f0d1d6bdf32fe935"}, {"title": "高级Java开发工程师", "company": "联想集团", "location": "苏州-市中心", "salary": "20K-35K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉Redis, MySQL, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 免费班车, 技术津贴, 下午茶, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.272099", "job_id": "a3bb2f704a3ff631"}, {"title": "Python数据工程师", "company": "快手", "location": "西安-高新区", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Spark, ETL, Hadoop; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 远程办公, 补充医疗保险, 培训机会, 带薪年假, 年终奖, 下午茶, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.272325", "job_id": "772ac8a4b644e45e"}, {"title": "Python数据工程师", "company": "网易", "location": "杭州-市中心", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉数据仓库, <PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 下午茶, 股票期权, 健身房, 补充医疗保险, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.272551", "job_id": "cfae652773c29904"}, {"title": "Python数据工程师", "company": "建设银行", "location": "合肥-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉<PERSON>, <PERSON><PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 年终奖, 免费午餐, 下午茶, 住房补贴, 绩效奖金, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.272710", "job_id": "7412582ed322be80"}, {"title": "高级Java开发工程师", "company": "滴滴出行", "location": "长沙-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉MySQL, Spring Boot, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 股票期权, 健身房, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.272878", "job_id": "7d0eb898b30fe6b2"}, {"title": "Python数据工程师", "company": "工商银行", "location": "杭州-经济开发区", "salary": "25K-40K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Hadoop, Spark, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 带薪年假, 绩效奖金, 五险一金, 远程办公, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.273029", "job_id": "3540e7399bf71da1"}, {"title": "高级Java开发工程师", "company": "联想集团", "location": "成都-市中心", "salary": "25K-45K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉微服务, MySQL, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 免费午餐, 远程办公, 股票期权, 五险一金, 技术津贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.273351", "job_id": "9882635f42df4f0d"}, {"title": "高级Java开发工程师", "company": "建设银行", "location": "昆明-市中心", "salary": "30K-50K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Kafka, Spring Boot, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 团建活动, 补充医疗保险, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.273505", "job_id": "b5a597dee3fc3e4b"}, {"title": "Python数据工程师", "company": "工商银行", "location": "深圳-龙岗区", "salary": "35K-60K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Python, ETL, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 年终奖, 团建活动, 带薪年假, 免费班车, 通讯补贴, 住房补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.273663", "job_id": "7be3d4c488eacb42"}, {"title": "Python数据工程师", "company": "百度", "location": "石家庄-高新区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉数据仓库, <PERSON><PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 五险一金, 技术津贴, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.273819", "job_id": "fc6a9e8834a9fb5a"}, {"title": "Python数据工程师", "company": "新浪", "location": "大连-市中心", "salary": "18K-30K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Spark, Python, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 下午茶, 股票期权, 交通补贴, 补充医疗保险, 通讯补贴, 团建活动, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.273966", "job_id": "6e45d8da18f929c9"}, {"title": "Python数据工程师", "company": "阿里巴巴", "location": "昆明-高新区", "salary": "25K-40K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Spark, ETL, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 通讯补贴, 弹性工作, 培训机会, 远程办公, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.274327", "job_id": "930a4d6a680ad161"}, {"title": "Python数据工程师", "company": "阿里巴巴", "location": "大连-高新区", "salary": "18K-30K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Spark, 数据仓库, <PERSON><PERSON>; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 远程办公, 补充医疗保险, 技术津贴, 住房补贴, 五险一金, 股票期权, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.274469", "job_id": "12e52703859df182"}, {"title": "Python数据工程师", "company": "百度", "location": "合肥-高新区", "salary": "35K-60K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Spark, Python, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 住房补贴, 技术津贴, 免费午餐, 通讯补贴, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.274626", "job_id": "aa46f0dae5dbb7ff"}, {"title": "Python数据工程师", "company": "陆金所", "location": "上海-长宁区", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉数据仓库, <PERSON><PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 绩效奖金, 团建活动, 股票期权, 培训机会, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.274839", "job_id": "b20f8fc294e707a0"}, {"title": "高级Java开发工程师", "company": "快手", "location": "北京-东城区", "salary": "30K-50K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Spring Boot, Kafka, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 下午茶, 技术津贴, 免费班车, 团建活动, 绩效奖金, 五险一金, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275025", "job_id": "0b01b4a5ec5fc3d3"}, {"title": "高级Java开发工程师", "company": "字节跳动", "location": "天津-市中心", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 通讯补贴, 远程办公, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275177", "job_id": "6741f26f504015d1"}, {"title": "高级Java开发工程师", "company": "字节跳动", "location": "济南-市中心", "salary": "25K-45K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Kafka, MySQL, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 带薪年假, 免费班车, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275330", "job_id": "f1d1bf0a235dda09"}, {"title": "高级Java开发工程师", "company": "京东", "location": "青岛-市中心", "salary": "30K-50K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON><PERSON><PERSON>, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 远程办公, 健身房, 免费午餐, 带薪年假, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275483", "job_id": "fefe8cc55579c53d"}, {"title": "Python数据工程师", "company": "蚂蚁集团", "location": "青岛-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Spark, <PERSON><PERSON>, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 股票期权, 弹性工作, 绩效奖金, 通讯补贴, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275636", "job_id": "1c56f1cb2902f361"}, {"title": "Python数据工程师", "company": "360", "location": "郑州-经济开发区", "salary": "18K-30K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉ETL, <PERSON><PERSON>, <PERSON>; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 股票期权, 健身房, 年终奖, 补充医疗保险, 交通补贴, 免费班车, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275779", "job_id": "c7b3b565e977bdcc"}, {"title": "高级Java开发工程师", "company": "阿里巴巴", "location": "沈阳-市中心", "salary": "25K-45K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉Redis, Kafka, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 远程办公, 年终奖, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.275916", "job_id": "6d33ca018ba7f8eb"}, {"title": "高级Java开发工程师", "company": "阿里巴巴", "location": "杭州-高新区", "salary": "30K-50K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉Java, 微服务, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 免费午餐, 远程办公, 团建活动, 补充医疗保险, 五险一金, 住房补贴, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276060", "job_id": "7b37ba4682bbb181"}, {"title": "高级Java开发工程师", "company": "小米", "location": "青岛-高新区", "salary": "25K-45K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉微服务, <PERSON><PERSON>, Kaf<PERSON>; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 下午茶, 弹性工作, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276196", "job_id": "9198284858b161a1"}, {"title": "高级Java开发工程师", "company": "美团", "location": "太原-经济开发区", "salary": "30K-50K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Kafka, 微服务, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 弹性工作, 免费午餐, 五险一金, 绩效奖金, 带薪年假, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276334", "job_id": "914d7dc5ad87322c"}, {"title": "Python数据工程师", "company": "蚂蚁集团", "location": "沈阳-高新区", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON><PERSON>, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 弹性工作, 免费午餐, 带薪年假, 通讯补贴, 培训机会, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276470", "job_id": "a5bd46c8a13a84ca"}, {"title": "Python数据工程师", "company": "建设银行", "location": "深圳-宝安区", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉ETL, Python, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 培训机会, 免费午餐, 健身房, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276600", "job_id": "603f05df2427596d"}, {"title": "Python数据工程师", "company": "滴滴出行", "location": "福州-经济开发区", "salary": "25K-40K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, 数据仓库, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 股票期权, 健身房, 远程办公, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276766", "job_id": "444a179f46a2c4d8"}, {"title": "Python数据工程师", "company": "平安科技", "location": "苏州-经济开发区", "salary": "25K-40K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, Python, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 弹性工作, 远程办公, 交通补贴, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.276906", "job_id": "e8447da63f5b7219"}, {"title": "高级Java开发工程师", "company": "新浪", "location": "石家庄-高新区", "salary": "30K-50K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉Spring Boot, Java, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 免费班车, 健身房, 培训机会, 年终奖, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277041", "job_id": "b3dedd025a26ee58"}, {"title": "高级Java开发工程师", "company": "陆金所", "location": "厦门-经济开发区", "salary": "25K-45K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Kafka, Redis, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 团建活动, 绩效奖金, 住房补贴, 年终奖, 通讯补贴, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277174", "job_id": "1e988cce1959fd1c"}, {"title": "Python数据工程师", "company": "华为", "location": "厦门-高新区", "salary": "18K-30K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Spark, 数据仓库, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 交通补贴, 免费午餐, 技术津贴, 团建活动, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277308", "job_id": "ee284d0eb865e3cc"}, {"title": "高级Java开发工程师", "company": "阿里巴巴", "location": "北京-东城区", "salary": "25K-45K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉MySQL, Java, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 团建活动, 免费班车, 通讯补贴, 交通补贴, 住房补贴, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277447", "job_id": "f7453b684ed5c704"}, {"title": "高级Java开发工程师", "company": "滴滴出行", "location": "北京-朝阳区", "salary": "25K-45K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Redis, MySQL, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 补充医疗保险, 弹性工作, 股票期权, 培训机会, 通讯补贴, 交通补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277581", "job_id": "586a2bef216bdbd6"}, {"title": "高级Java开发工程师", "company": "快手", "location": "重庆-经济开发区", "salary": "30K-50K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉Spring Boot, Java, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 交通补贴, 弹性工作, 远程办公, 绩效奖金, 下午茶, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277716", "job_id": "bdb0c72e04b2ad59"}, {"title": "高级Java开发工程师", "company": "中兴通讯", "location": "上海-浦东新区", "salary": "30K-50K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Java, 微服务, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 技术津贴, 弹性工作, 绩效奖金, 交通补贴, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.277862", "job_id": "d9b595de725bf9a6"}, {"title": "Python数据工程师", "company": "网易", "location": "沈阳-高新区", "salary": "18K-30K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉<PERSON>, <PERSON><PERSON>, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 免费班车, 技术津贴, 健身房, 培训机会, 下午茶, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.278106", "job_id": "e509ac20319a546e"}, {"title": "高级Java开发工程师", "company": "美团", "location": "合肥-高新区", "salary": "20K-35K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉Spring Boot, Kafka, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 交通补贴, 技术津贴, 年终奖, 绩效奖金, 培训机会, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.278295", "job_id": "d273194aa6652461"}, {"title": "高级Java开发工程师", "company": "蚂蚁集团", "location": "重庆-市中心", "salary": "30K-50K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉MySQL, 微服务, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 技术津贴, 住房补贴, 弹性工作, 健身房, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.278460", "job_id": "9c3c84c2c59836cd"}, {"title": "高级Java开发工程师", "company": "平安科技", "location": "南京-市中心", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Java, 微服务, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 补充医疗保险, 绩效奖金, 免费班车, 下午茶, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.278613", "job_id": "c41f36910815f3fa"}, {"title": "Python数据工程师", "company": "腾讯", "location": "武汉-经济开发区", "salary": "18K-30K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Spark, ETL, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 交通补贴, 绩效奖金, 免费午餐, 下午茶, 远程办公, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.278760", "job_id": "70a83514dc528c85"}, {"title": "Python数据工程师", "company": "百度", "location": "青岛-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉ETL, Python, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 补充医疗保险, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.278914", "job_id": "b7562d2b77e68157"}, {"title": "高级Java开发工程师", "company": "陆金所", "location": "北京-海淀区", "salary": "20K-35K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉Spring Boot, Java, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 住房补贴, 补充医疗保险, 培训机会, 弹性工作, 团建活动, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.279059", "job_id": "8366291ed2caca0d"}, {"title": "Python数据工程师", "company": "阿里巴巴", "location": "太原-高新区", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON><PERSON>, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 通讯补贴, 补充医疗保险, 下午茶, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.279202", "job_id": "35c4539f36609847"}, {"title": "Python数据工程师", "company": "陆金所", "location": "沈阳-市中心", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON><PERSON>, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 健身房, 弹性工作, 年终奖, 带薪年假, 交通补贴, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.279354", "job_id": "437a4805ffe5e6b4"}, {"title": "Python数据工程师", "company": "美团", "location": "长沙-市中心", "salary": "35K-60K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉ETL, Spark, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 下午茶, 团建活动, 年终奖, 远程办公, 技术津贴, 培训机会, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.279510", "job_id": "b914e100c2215017"}, {"title": "高级Java开发工程师", "company": "快手", "location": "沈阳-高新区", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Spring Boot, <PERSON><PERSON>, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 下午茶, 弹性工作, 住房补贴, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.279658", "job_id": "d6ec2eaaa76ecc7b"}, {"title": "Python数据工程师", "company": "新浪", "location": "济南-市中心", "salary": "25K-40K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Hadoop, ETL, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 带薪年假, 年终奖, 技术津贴, 团建活动, 远程办公, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.279810", "job_id": "a7a7944ba9faac13"}, {"title": "高级Java开发工程师", "company": "滴滴出行", "location": "济南-经济开发区", "salary": "20K-35K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉Redis, MySQL, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 培训机会, 技术津贴, 住房补贴, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280000", "job_id": "b773938a0eb1c091"}, {"title": "高级Java开发工程师", "company": "腾讯", "location": "郑州-高新区", "salary": "25K-45K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉Redis, MySQL, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 免费午餐, 年终奖, 培训机会, 通讯补贴, 补充医疗保险, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280157", "job_id": "615100fae9b532c8"}, {"title": "高级Java开发工程师", "company": "百度", "location": "厦门-高新区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Spring Boot, Kaf<PERSON>, <PERSON><PERSON>; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 五险一金, 健身房, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280307", "job_id": "df53c5cb81196399"}, {"title": "Python数据工程师", "company": "360", "location": "北京-朝阳区", "salary": "25K-40K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Spark, 数据仓库, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 交通补贴, 带薪年假, 弹性工作, 五险一金, 通讯补贴, 免费班车, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280452", "job_id": "902e171de204cc84"}, {"title": "Python数据工程师", "company": "搜狐", "location": "深圳-福田区", "salary": "18K-30K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉ETL, Python, Hadoop; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 带薪年假, 年终奖, 技术津贴, 交通补贴, 免费午餐, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280592", "job_id": "bdb476b26c7ec1c8"}, {"title": "高级Java开发工程师", "company": "小米", "location": "上海-静安区", "salary": "20K-35K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Redis, Spring Boot, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 股票期权, 远程办公, 交通补贴, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280727", "job_id": "39e719136f0236d8"}, {"title": "Python数据工程师", "company": "蚂蚁集团", "location": "上海-徐汇区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉Spark, <PERSON><PERSON>, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 技术津贴, 补充医疗保险, 住房补贴, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280859", "job_id": "ac00402bfad51e31"}, {"title": "Python数据工程师", "company": "360", "location": "天津-高新区", "salary": "25K-40K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉Python, Spark, Hadoop; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 带薪年假, 免费午餐, 交通补贴, 远程办公, 弹性工作, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.280993", "job_id": "c21bb9d015dd6e09"}, {"title": "高级Java开发工程师", "company": "字节跳动", "location": "北京-东城区", "salary": "20K-35K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Kafka, MySQL, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 远程办公, 交通补贴, 通讯补贴, 绩效奖金, 下午茶, 带薪年假, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.281125", "job_id": "214f34e7a134f5bf"}, {"title": "Python数据工程师", "company": "滴滴出行", "location": "合肥-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉ETL, Hadoop, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 免费午餐, 股票期权, 年终奖, 技术津贴, 弹性工作, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.281263", "job_id": "3a8a2ba13ecc3f95"}, {"title": "高级Java开发工程师", "company": "招商银行", "location": "北京-海淀区", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉微服务, MySQL, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 技术津贴, 培训机会, 带薪年假, 弹性工作, 远程办公, 五险一金, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.281413", "job_id": "4e13348bf61ed01e"}, {"title": "Python数据工程师", "company": "联想集团", "location": "南京-市中心", "salary": "25K-40K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉ETL, <PERSON><PERSON>, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 补充医疗保险, 带薪年假, 团建活动, 免费班车, 技术津贴, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.281666", "job_id": "cbbdaf5b7261127e"}, {"title": "Python数据工程师", "company": "蚂蚁集团", "location": "北京-西城区", "salary": "35K-60K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Spark, <PERSON><PERSON>, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 团建活动, 弹性工作, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.281854", "job_id": "a08ee8e41c14d09a"}, {"title": "高级Java开发工程师", "company": "美团", "location": "大连-经济开发区", "salary": "25K-45K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Java, Redis, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 带薪年假, 住房补贴, 弹性工作, 补充医疗保险, 年终奖, 免费午餐, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282013", "job_id": "8e5ae30ead50fcef"}, {"title": "高级Java开发工程师", "company": "阿里巴巴", "location": "昆明-市中心", "salary": "20K-35K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Kafka, Java, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 通讯补贴, 培训机会, 健身房, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282162", "job_id": "b52b9705bb8948fe"}, {"title": "高级Java开发工程师", "company": "网易", "location": "西安-市中心", "salary": "30K-50K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Redis, Spring Boot, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 技术津贴, 交通补贴, 弹性工作, 带薪年假, 年终奖, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282309", "job_id": "0633ff652dd519c5"}, {"title": "高级Java开发工程师", "company": "华为", "location": "青岛-市中心", "salary": "20K-35K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Java, Redis, MySQL; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 五险一金, 交通补贴, 技术津贴, 股票期权, 弹性工作, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282457", "job_id": "92a24e1b6d487d49"}, {"title": "Python数据工程师", "company": "搜狐", "location": "郑州-高新区", "salary": "25K-40K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉数据仓库, <PERSON><PERSON>, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 年终奖, 住房补贴, 通讯补贴, 带薪年假, 补充医疗保险, 股票期权, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282592", "job_id": "218126770422ed54"}, {"title": "Python数据工程师", "company": "滴滴出行", "location": "济南-市中心", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Spark, ETL, Hadoop; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 技术津贴, 通讯补贴, 绩效奖金, 免费午餐, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282731", "job_id": "25cd7c6543a1537e"}, {"title": "高级Java开发工程师", "company": "滴滴出行", "location": "青岛-高新区", "salary": "20K-35K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Java, Kafka, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 五险一金, 下午茶, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.282864", "job_id": "e629a760a848e300"}, {"title": "高级Java开发工程师", "company": "京东", "location": "合肥-市中心", "salary": "30K-50K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Spring Boot, Kafka, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 年终奖, 带薪年假, 技术津贴, 弹性工作, 下午茶, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283008", "job_id": "003515e8c9e276a8"}, {"title": "Python数据工程师", "company": "腾讯", "location": "广州-天河区", "salary": "25K-40K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉Had<PERSON>, ETL, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 补充医疗保险, 股票期权, 培训机会, 交通补贴, 住房补贴, 绩效奖金, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283142", "job_id": "2942fd75e870a03b"}, {"title": "Python数据工程师", "company": "阿里巴巴", "location": "广州-白云区", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON><PERSON>, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 团建活动, 免费午餐, 补充医疗保险, 健身房, 远程办公, 交通补贴, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283282", "job_id": "0e015fc1f3e3fcfe"}, {"title": "高级Java开发工程师", "company": "陆金所", "location": "厦门-高新区", "salary": "25K-45K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉MySQL, Java, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 住房补贴, 年终奖, 下午茶, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283418", "job_id": "d98d4d4e188aba3c"}, {"title": "高级Java开发工程师", "company": "京东", "location": "上海-徐汇区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Kafka, Java, 微服务; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 补充医疗保险, 绩效奖金, 远程办公, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283551", "job_id": "4bb5c06f2872bc80"}, {"title": "高级Java开发工程师", "company": "滴滴出行", "location": "沈阳-经济开发区", "salary": "30K-50K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Redis, MySQL, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 绩效奖金, 免费班车, 远程办公, 技术津贴, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283693", "job_id": "c9e8767939a8b7b3"}, {"title": "高级Java开发工程师", "company": "滴滴出行", "location": "合肥-经济开发区", "salary": "20K-35K", "education": "硕士", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉微服务, MySQL, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 补充医疗保险, 健身房, 股票期权, 带薪年假, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283834", "job_id": "23cb76b14451567a"}, {"title": "高级Java开发工程师", "company": "华为", "location": "深圳-南山区", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉微服务, Java, Spring Boot; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 五险一金, 培训机会, 免费午餐, 团建活动, 通讯补贴, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.283973", "job_id": "05a5a8e0628653d7"}, {"title": "Python数据工程师", "company": "建设银行", "location": "合肥-高新区", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据仓库, Spark, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 股票期权, 远程办公, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284104", "job_id": "4c0f665e85437111"}, {"title": "Python数据工程师", "company": "阿里巴巴", "location": "合肥-高新区", "salary": "25K-40K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Spark, Hadoop, ETL; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 交通补贴, 股票期权, 免费午餐, 年终奖, 通讯补贴, 带薪年假, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284234", "job_id": "4fe7fba7ee4b22d2"}, {"title": "Python数据工程师", "company": "新浪", "location": "石家庄-高新区", "salary": "18K-30K", "education": "本科", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉ETL, <PERSON><PERSON>, <PERSON>; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 健身房, 股票期权, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284372", "job_id": "be7a5b66a30a085b"}, {"title": "Python数据工程师", "company": "小米", "location": "上海-徐汇区", "salary": "25K-40K", "education": "本科", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据仓库, ETL, Python; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 健身房, 培训机会, 交通补贴, 远程办公, 绩效奖金, 带薪年假, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284504", "job_id": "65f8059ad5f77b2d"}, {"title": "Python数据工程师", "company": "蚂蚁集团", "location": "厦门-市中心", "salary": "25K-40K", "education": "硕士", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉<PERSON>, <PERSON><PERSON>, 数据仓库; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 培训机会, 健身房, 五险一金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284633", "job_id": "751ecf3757a87ab2"}, {"title": "高级Java开发工程师", "company": "新浪", "location": "苏州-高新区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉Redis, 微服务, MySQL; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 弹性工作, 股票期权, 带薪年假, 免费午餐, 五险一金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284769", "job_id": "9464671e3b5a2fc4"}, {"title": "Python数据工程师", "company": "华为", "location": "郑州-高新区", "salary": "25K-40K", "education": "硕士", "experience": "5-12年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉Python, 数据仓库, <PERSON><PERSON>; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 年终奖, 补充医疗保险, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.284924", "job_id": "e92e5437b5cb3529"}, {"title": "高级Java开发工程师", "company": "腾讯", "location": "合肥-市中心", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Spring Boot, MySQL, Java; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 团建活动, 远程办公, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285055", "job_id": "59666d97f2d17376"}, {"title": "高级Java开发工程师", "company": "建设银行", "location": "长沙-高新区", "salary": "25K-45K", "education": "本科", "experience": "3-5年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉MySQL, Kafka, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 补充医疗保险, 住房补贴, 五险一金, 股票期权, 交通补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285189", "job_id": "b76e29f4e67c05bb"}, {"title": "高级Java开发工程师", "company": "中兴通讯", "location": "沈阳-市中心", "salary": "20K-35K", "education": "本科", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 本科及以上学历; 熟悉Spring Boot, Java, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 年终奖, 免费午餐, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285322", "job_id": "98c0615cd6bbbde5"}, {"title": "高级Java开发工程师", "company": "京东", "location": "北京-丰台区", "salary": "25K-45K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉MySQL, Java, Kafka; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 补充医疗保险, 远程办公, 健身房, 技术津贴, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285456", "job_id": "c99a2e90de32c196"}, {"title": "Python数据工程师", "company": "字节跳动", "location": "上海-浦东新区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 补充医疗保险, 免费班车, 健身房, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285592", "job_id": "8ac5d9e8743b3325"}, {"title": "高级Java开发工程师", "company": "字节跳动", "location": "济南-高新区", "salary": "30K-50K", "education": "硕士", "experience": "5-8年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Kafka, MySQL, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 绩效奖金, 远程办公, 通讯补贴, 弹性工作, 补充医疗保险, 五险一金, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285739", "job_id": "c60a1fef1b640017"}, {"title": "高级Java开发工程师", "company": "阿里巴巴", "location": "福州-高新区", "salary": "20K-35K", "education": "硕士", "experience": "8-15年", "description": "负责核心业务系统开发，参与架构设计和技术选型", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉MySQL, 微服务, Redis; 熟悉分布式系统设计，有大型项目经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 弹性工作, 绩效奖金, 健身房, 股票期权, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.285878", "job_id": "a49d08237d949653"}, {"title": "Python数据工程师", "company": "360", "location": "福州-经济开发区", "salary": "25K-40K", "education": "本科", "experience": "3-8年", "description": "负责大数据平台建设，数据处理和分析", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉数据仓库, <PERSON><PERSON>, Spark; 熟悉大数据技术栈，有数据建模经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 健身房, 五险一金, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286012", "job_id": "97217c7db1a4ffd8"}, {"title": "产品经理", "company": "旷视科技", "location": "杭州-市中心", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉用户研究, 项目管理, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 补充医疗保险, 股票期权, 通讯补贴, 培训机会, 下午茶, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286150", "job_id": "08c23316c4d26316"}, {"title": "产品经理", "company": "科大讯飞", "location": "合肥-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉项目管理, 数据分析, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 住房补贴, 补充医疗保险, 技术津贴, 通讯补贴, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286299", "job_id": "7bfe7925551f<PERSON>a"}, {"title": "产品经理", "company": "紫光集团", "location": "合肥-市中心", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉数据分析, 产品设计, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 股票期权, 补充医疗保险, 五险一金, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286438", "job_id": "342000074d00df86"}, {"title": "产品经理", "company": "海康威视", "location": "厦门-市中心", "salary": "35K-60K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉项目管理, 数据分析, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 通讯补贴, 五险一金, 补充医疗保险, 远程办公, 绩效奖金, 带薪年假, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286575", "job_id": "8e8ed3bdcc39a789"}, {"title": "DevOps工程师", "company": "东软集团", "location": "北京-海淀区", "salary": "28K-50K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, 监控, Docker; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 健身房, 免费午餐, 下午茶, 带薪年假, 年终奖, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286729", "job_id": "b66897426a8728d6"}, {"title": "产品经理", "company": "明略科技", "location": "北京-西城区", "salary": "18K-30K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉数据分析, 产品设计, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 团建活动, 远程办公, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.286875", "job_id": "e2813d7510dda4bc"}, {"title": "算法工程师", "company": "华胜天成", "location": "南京-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉深度学习, 算法优化, 机器学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 住房补贴, 交通补贴, 远程办公, 技术津贴, 通讯补贴, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287025", "job_id": "f31dca0ed2eca911"}, {"title": "产品经理", "company": "文思海辉", "location": "天津-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据分析, 产品设计, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 弹性工作, 下午茶, 免费班车, 团建活动, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287174", "job_id": "28b4a1be40b40c6e"}, {"title": "算法工程师", "company": "商汤科技", "location": "北京-西城区", "salary": "35K-60K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉PyT<PERSON><PERSON>, 算法优化, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 免费班车, 弹性工作, 团建活动, 五险一金, 下午茶, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287315", "job_id": "d6a762be05c9f8fd"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "济南-市中心", "salary": "20K-35K", "education": "硕士", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉监控, <PERSON>er, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 弹性工作, 住房补贴, 补充医疗保险, 团建活动, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287456", "job_id": "9316024d8ee7dc02"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "太原-市中心", "salary": "20K-35K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉Jenkins, 自动化, <PERSON>er; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 下午茶, 绩效奖金, 团建活动, 带薪年假, 健身房, 弹性工作, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287600", "job_id": "b0e0146f5c7af07a"}, {"title": "DevOps工程师", "company": "中软国际", "location": "天津-市中心", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Docker; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 培训机会, 健身房, 远程办公, 年终奖, 团建活动, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287740", "job_id": "00310e2caa4f2d40"}, {"title": "产品经理", "company": "汇顶科技", "location": "郑州-高新区", "salary": "25K-45K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉数据分析, 产品设计, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 通讯补贴, 免费班车, 带薪年假, 补充医疗保险, 年终奖, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.287876", "job_id": "0e93bd63b875d8c1"}, {"title": "产品经理", "company": "商汤科技", "location": "广州-越秀区", "salary": "25K-45K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉数据分析, 项目管理, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 补充医疗保险, 住房补贴, 下午茶, 年终奖, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288026", "job_id": "82cbaf611b56b51a"}, {"title": "DevOps工程师", "company": "依图科技", "location": "北京-西城区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Docker, 自动化, <PERSON>ber<PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 绩效奖金, 股票期权, 技术津贴, 通讯补贴, 交通补贴, 培训机会, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288165", "job_id": "996fca998c614bb8"}, {"title": "算法工程师", "company": "大华股份", "location": "大连-市中心", "salary": "35K-60K", "education": "博士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 博士及以上学历; 熟悉算法优化, 机器学习, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 交通补贴, 培训机会, 带薪年假, 远程办公, 技术津贴, 下午茶, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288305", "job_id": "9b98fdd1466cd812"}, {"title": "DevOps工程师", "company": "紫光集团", "location": "北京-西城区", "salary": "28K-50K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON>, <PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 五险一金, 培训机会, 年终奖, 住房补贴, 股票期权, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288450", "job_id": "250f8d0e31e12dc7"}, {"title": "产品经理", "company": "汇顶科技", "location": "合肥-高新区", "salary": "25K-45K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉项目管理, 用户研究, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 年终奖, 免费班车, 绩效奖金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288634", "job_id": "b831f3d53620f52a"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "西安-高新区", "salary": "35K-60K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉<PERSON>, <PERSON>er, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 团建活动, 绩效奖金, 免费午餐, 带薪年假, 远程办公, 补充医疗保险, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288796", "job_id": "8b7fbe56bb290eb9"}, {"title": "产品经理", "company": "东软集团", "location": "广州-海珠区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉数据分析, 用户研究, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 团建活动, 免费午餐, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.288932", "job_id": "7bbc62855ade9152"}, {"title": "算法工程师", "company": "汇顶科技", "location": "深圳-宝安区", "salary": "45K-80K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉深度学习, <PERSON>y<PERSON><PERSON><PERSON>, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 培训机会, 股票期权, 年终奖, 健身房, 技术津贴, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289081", "job_id": "9cea1cf7467bdca7"}, {"title": "产品经理", "company": "商汤科技", "location": "天津-高新区", "salary": "25K-45K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉数据分析, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 住房补贴, 培训机会, 下午茶, 免费班车, 通讯补贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289240", "job_id": "0144ccaceca311c1"}, {"title": "DevOps工程师", "company": "中软国际", "location": "苏州-市中心", "salary": "20K-35K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉监控, <PERSON>er, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 五险一金, 健身房, 年终奖, 技术津贴, 交通补贴, 补充医疗保险, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289382", "job_id": "00b2df3a83b004cd"}, {"title": "DevOps工程师", "company": "大华股份", "location": "深圳-罗湖区", "salary": "28K-50K", "education": "硕士", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 五险一金, 绩效奖金, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289516", "job_id": "50cafecef32faafc"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "广州-白云区", "salary": "20K-35K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 弹性工作, 远程办公, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289650", "job_id": "d1ec37a7ab385aad"}, {"title": "DevOps工程师", "company": "中软国际", "location": "苏州-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 培训机会, 绩效奖金, 免费班车, 通讯补贴, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289801", "job_id": "d52ad58816c918e2"}, {"title": "产品经理", "company": "中软国际", "location": "武汉-市中心", "salary": "25K-45K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉产品设计, 数据分析, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 免费午餐, 团建活动, 绩效奖金, 五险一金, 股票期权, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.289941", "job_id": "aa28720e1b56f215"}, {"title": "产品经理", "company": "海康威视", "location": "天津-市中心", "salary": "25K-45K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉产品设计, 项目管理, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 交通补贴, 带薪年假, 通讯补贴, 住房补贴, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.290105", "job_id": "8cebffb442578709"}, {"title": "产品经理", "company": "旷视科技", "location": "太原-市中心", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据分析, 产品设计, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 住房补贴, 培训机会, 免费班车, 免费午餐, 团建活动, 弹性工作, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.290285", "job_id": "410726d1ef0a8070"}, {"title": "产品经理", "company": "明略科技", "location": "苏州-经济开发区", "salary": "25K-45K", "education": "硕士", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉项目管理, 数据分析, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 下午茶, 交通补贴, 绩效奖金, 弹性工作, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.290479", "job_id": "aa8b784701caa0ec"}, {"title": "算法工程师", "company": "依图科技", "location": "合肥-经济开发区", "salary": "45K-80K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉PyT<PERSON>ch, 算法优化, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 交通补贴, 弹性工作, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.290631", "job_id": "526382499bcc0621"}, {"title": "DevOps工程师", "company": "中软国际", "location": "合肥-市中心", "salary": "28K-50K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉自动化, <PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 技术津贴, 股票期权, 免费班车, 免费午餐, 通讯补贴, 带薪年假, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.290787", "job_id": "3d30c257f63b8f02"}, {"title": "算法工程师", "company": "中软国际", "location": "郑州-经济开发区", "salary": "25K-45K", "education": "博士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 博士及以上学历; 熟悉TensorFlow, 算法优化, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 培训机会, 补充医疗保险, 技术津贴, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.290930", "job_id": "e2251134aac6b20b"}, {"title": "产品经理", "company": "明略科技", "location": "成都-市中心", "salary": "25K-45K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉用户研究, 数据分析, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 免费班车, 带薪年假, 交通补贴, 通讯补贴, 培训机会, 免费午餐, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291072", "job_id": "8bf2400cd7d82ed4"}, {"title": "产品经理", "company": "博彦科技", "location": "济南-高新区", "salary": "18K-30K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉产品设计, 项目管理, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 团建活动, 远程办公, 股票期权, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291209", "job_id": "1736977a1ebc64d7"}, {"title": "产品经理", "company": "大华股份", "location": "深圳-宝安区", "salary": "35K-60K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据分析, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 住房补贴, 带薪年假, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291347", "job_id": "c6cb1208ffb33cd8"}, {"title": "DevOps工程师", "company": "中软国际", "location": "长沙-高新区", "salary": "35K-60K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON>, <PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 住房补贴, 弹性工作, 培训机会, 交通补贴, 股票期权, 免费午餐, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291508", "job_id": "1c4d1daba7a4dd90"}, {"title": "产品经理", "company": "紫光集团", "location": "郑州-市中心", "salary": "25K-45K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉数据分析, 用户研究, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 带薪年假, 免费午餐, 五险一金, 住房补贴, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291653", "job_id": "0df61baa4c8defea"}, {"title": "DevOps工程师", "company": "紫光集团", "location": "杭州-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉Kubernet<PERSON>, 自动化, <PERSON>er; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 住房补贴, 弹性工作, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291790", "job_id": "80b9e40ca3985841"}, {"title": "DevOps工程师", "company": "商汤科技", "location": "南京-高新区", "salary": "20K-35K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 下午茶, 弹性工作, 远程办公, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.291926", "job_id": "b7e05ed2e6835b07"}, {"title": "产品经理", "company": "大华股份", "location": "福州-市中心", "salary": "18K-30K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉项目管理, 用户研究, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 带薪年假, 年终奖, 绩效奖金, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292063", "job_id": "a5ba531468982ef0"}, {"title": "产品经理", "company": "东软集团", "location": "沈阳-高新区", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉用户研究, 数据分析, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 通讯补贴, 免费午餐, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292196", "job_id": "28923bbfb9e7de29"}, {"title": "算法工程师", "company": "第四范式", "location": "杭州-经济开发区", "salary": "45K-80K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉PyT<PERSON>ch, 机器学习, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 补充医疗保险, 弹性工作, 免费午餐, 带薪年假, 股票期权, 交通补贴, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292333", "job_id": "6e03edb68611f389"}, {"title": "产品经理", "company": "东软集团", "location": "重庆-高新区", "salary": "25K-45K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉项目管理, 产品设计, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 五险一金, 股票期权, 通讯补贴, 免费午餐, 弹性工作, 绩效奖金, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292465", "job_id": "1cdf5c9f2609663e"}, {"title": "DevOps工程师", "company": "中软国际", "location": "石家庄-市中心", "salary": "28K-50K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉Kubernetes, 自动化, 监控; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 免费午餐, 带薪年假, 五险一金, 补充医疗保险, 远程办公, 弹性工作, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292602", "job_id": "058a8ceab8d6e0c7"}, {"title": "产品经理", "company": "紫光集团", "location": "武汉-市中心", "salary": "18K-30K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据分析, 产品设计, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 弹性工作, 股票期权, 绩效奖金, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292730", "job_id": "5e6628108c7a621c"}, {"title": "算法工程师", "company": "东软集团", "location": "西安-高新区", "salary": "35K-60K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉PyT<PERSON>ch, 算法优化, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 下午茶, 五险一金, 免费班车, 弹性工作, 技术津贴, 健身房, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.292862", "job_id": "d383a8e64a693cdd"}, {"title": "算法工程师", "company": "汇顶科技", "location": "郑州-高新区", "salary": "25K-45K", "education": "硕士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 硕士及以上学历; 熟悉TensorFlow, 算法优化, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 培训机会, 团建活动, 远程办公, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293005", "job_id": "6869c4bec06155ab"}, {"title": "算法工程师", "company": "海康威视", "location": "武汉-经济开发区", "salary": "25K-45K", "education": "博士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 博士及以上学历; 熟悉Tensor<PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 五险一金, 通讯补贴, 交通补贴, 团建活动, 股票期权, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293165", "job_id": "85fd71af827c3036"}, {"title": "DevOps工程师", "company": "旷视科技", "location": "太原-市中心", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Jenkins, 监控, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 免费班车, 绩效奖金, 健身房, 弹性工作, 技术津贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293322", "job_id": "a44a4e86873f029e"}, {"title": "算法工程师", "company": "中软国际", "location": "石家庄-市中心", "salary": "35K-60K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉算法优化, TensorFlow, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 免费班车, 健身房, 补充医疗保险, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293466", "job_id": "9918929de4dfde8f"}, {"title": "DevOps工程师", "company": "明略科技", "location": "南京-经济开发区", "salary": "28K-50K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 培训机会, 免费午餐, 年终奖, 下午茶, 补充医疗保险, 健身房, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293603", "job_id": "d191c71ab4c09a65"}, {"title": "DevOps工程师", "company": "中软国际", "location": "北京-丰台区", "salary": "20K-35K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉Docker, <PERSON><PERSON><PERSON><PERSON>, 监控; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 股票期权, 带薪年假, 通讯补贴, 健身房, 免费午餐, 培训机会, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293742", "job_id": "dfdb14730fd2a8d7"}, {"title": "DevOps工程师", "company": "依图科技", "location": "深圳-福田区", "salary": "28K-50K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>er, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 培训机会, 股票期权, 远程办公, 绩效奖金, 下午茶, 团建活动, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.293877", "job_id": "5a30f23c94730482"}, {"title": "产品经理", "company": "汇顶科技", "location": "重庆-高新区", "salary": "35K-60K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉用户研究, 数据分析, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 五险一金, 技术津贴, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294016", "job_id": "1fbf00d3ad5e3801"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "西安-市中心", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 免费班车, 通讯补贴, 下午茶, 技术津贴, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294167", "job_id": "03eb8f4d09cca986"}, {"title": "DevOps工程师", "company": "明略科技", "location": "太原-市中心", "salary": "35K-60K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, 监控; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 弹性工作, 远程办公, 五险一金, 年终奖, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294320", "job_id": "4f646791db1957b9"}, {"title": "算法工程师", "company": "华胜天成", "location": "重庆-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉算法优化, TensorFlow, 机器学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 远程办公, 培训机会, 技术津贴, 住房补贴, 免费午餐, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294462", "job_id": "d6793894524c4157"}, {"title": "DevOps工程师", "company": "科大讯飞", "location": "北京-丰台区", "salary": "35K-60K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉Jenkins, 监控, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 下午茶, 通讯补贴, 团建活动, 住房补贴, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294602", "job_id": "3027922a2fd91e5d"}, {"title": "算法工程师", "company": "软通动力", "location": "杭州-市中心", "salary": "25K-45K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉算法优化, TensorFlow, 机器学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 技术津贴, 弹性工作, 股票期权, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294751", "job_id": "ea48bede940179fe"}, {"title": "产品经理", "company": "第四范式", "location": "北京-东城区", "salary": "25K-45K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉项目管理, 数据分析, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 技术津贴, 健身房, 住房补贴, 绩效奖金, 年终奖, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.294898", "job_id": "4168c9487ce70125"}, {"title": "DevOps工程师", "company": "东软集团", "location": "广州-荔湾区", "salary": "35K-60K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉监控, <PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 免费班车, 培训机会, 五险一金, 免费午餐, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295057", "job_id": "597e881d5844af18"}, {"title": "DevOps工程师", "company": "东软集团", "location": "大连-高新区", "salary": "35K-60K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉自动化, <PERSON>er, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 通讯补贴, 带薪年假, 弹性工作, 远程办公, 交通补贴, 健身房, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295190", "job_id": "5cd99f24abcdf5f3"}, {"title": "产品经理", "company": "紫光集团", "location": "重庆-市中心", "salary": "35K-60K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉产品设计, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 健身房, 补充医疗保险, 绩效奖金, 住房补贴, 免费午餐, 免费班车, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295356", "job_id": "18deff2cb54b097c"}, {"title": "DevOps工程师", "company": "中软国际", "location": "昆明-市中心", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉自动化, <PERSON>, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 通讯补贴, 免费班车, 住房补贴, 补充医疗保险, 健身房, 免费午餐, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295494", "job_id": "77c8bf35e78812df"}, {"title": "DevOps工程师", "company": "第四范式", "location": "郑州-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化, 监控, Docker; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 五险一金, 通讯补贴, 年终奖, 技术津贴, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295622", "job_id": "22703831be938b1e"}, {"title": "产品经理", "company": "中软国际", "location": "西安-高新区", "salary": "25K-45K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉数据分析, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 通讯补贴, 交通补贴, 免费班车, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295747", "job_id": "e05dd7e8d199bd55"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "上海-徐汇区", "salary": "35K-60K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉Jenkins, 自动化, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 技术津贴, 股票期权, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.295878", "job_id": "97f6d26f3b6dce11"}, {"title": "DevOps工程师", "company": "第四范式", "location": "济南-市中心", "salary": "28K-50K", "education": "本科", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化, <PERSON><PERSON><PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 交通补贴, 免费午餐, 住房补贴, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296015", "job_id": "cc939ee052c230e3"}, {"title": "产品经理", "company": "海康威视", "location": "昆明-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉项目管理, 用户研究, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 五险一金, 通讯补贴, 带薪年假, 股票期权, 住房补贴, 团建活动, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296144", "job_id": "2f2dc6ee38f44a0c"}, {"title": "DevOps工程师", "company": "汇顶科技", "location": "郑州-高新区", "salary": "28K-50K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉监控, <PERSON><PERSON><PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 远程办公, 绩效奖金, 下午茶, 年终奖, 团建活动, 住房补贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296281", "job_id": "a99ff2ae6e5fb303"}, {"title": "算法工程师", "company": "博彦科技", "location": "深圳-福田区", "salary": "35K-60K", "education": "博士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 博士及以上学历; 熟悉算法优化, TensorFlow, 机器学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 团建活动, 通讯补贴, 免费班车, 弹性工作, 交通补贴, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296410", "job_id": "1e4e024ffb067525"}, {"title": "产品经理", "company": "大华股份", "location": "武汉-高新区", "salary": "25K-45K", "education": "本科", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据分析, 项目管理, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 年终奖, 免费班车, 下午茶, 绩效奖金, 五险一金, 健身房, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296540", "job_id": "cf559f28c3d890d2"}, {"title": "算法工程师", "company": "东软集团", "location": "上海-浦东新区", "salary": "25K-45K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉Tensor<PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, 算法优化; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 培训机会, 股票期权, 下午茶, 补充医疗保险, 交通补贴, 免费班车, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296708", "job_id": "5532a7b6a517cddb"}, {"title": "产品经理", "company": "博彦科技", "location": "南京-经济开发区", "salary": "25K-45K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉数据分析, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 培训机会, 远程办公, 免费班车, 住房补贴, 弹性工作, 健身房, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.296896", "job_id": "dc23f8c97dba3aa6"}, {"title": "DevOps工程师", "company": "华胜天成", "location": "天津-市中心", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, 监控, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 住房补贴, 交通补贴, 通讯补贴, 免费班车, 绩效奖金, 健身房, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297056", "job_id": "f42ba1be276e4ad6"}, {"title": "产品经理", "company": "软通动力", "location": "沈阳-经济开发区", "salary": "18K-30K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉项目管理, 用户研究, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 绩效奖金, 健身房, 弹性工作, 住房补贴, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297206", "job_id": "b12f1a6c205a0852"}, {"title": "DevOps工程师", "company": "博彦科技", "location": "武汉-市中心", "salary": "28K-50K", "education": "硕士", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, 监控, <PERSON>er; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 通讯补贴, 五险一金, 远程办公, 交通补贴, 弹性工作, 下午茶, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297355", "job_id": "8954c953607c596d"}, {"title": "产品经理", "company": "旷视科技", "location": "石家庄-高新区", "salary": "25K-45K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉产品设计, 用户研究, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 股票期权, 交通补贴, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297492", "job_id": "41fbcd6f6c098eca"}, {"title": "产品经理", "company": "依图科技", "location": "北京-丰台区", "salary": "25K-45K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉产品设计, 项目管理, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 通讯补贴, 补充医疗保险, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297626", "job_id": "c0ee81f61cd40aea"}, {"title": "产品经理", "company": "云从科技", "location": "天津-高新区", "salary": "25K-45K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉项目管理, 用户研究, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 股票期权, 通讯补贴, 免费午餐, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297758", "job_id": "615fe67ce7ee9e60"}, {"title": "算法工程师", "company": "海康威视", "location": "青岛-经济开发区", "salary": "35K-60K", "education": "博士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 博士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON><PERSON>, Tensor<PERSON><PERSON>, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 技术津贴, 住房补贴, 股票期权, 团建活动, 弹性工作, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.297896", "job_id": "e62089a363371275"}, {"title": "产品经理", "company": "大华股份", "location": "杭州-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉用户研究, 产品设计, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 交通补贴, 下午茶, 免费班车, 绩效奖金, 团建活动, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298028", "job_id": "aedd82cddf3389da"}, {"title": "DevOps工程师", "company": "大华股份", "location": "合肥-市中心", "salary": "35K-60K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉<PERSON>, <PERSON>er, 监控; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 健身房, 绩效奖金, 补充医疗保险, 股票期权, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298165", "job_id": "bbee68ddc69625c9"}, {"title": "DevOps工程师", "company": "紫光集团", "location": "福州-经济开发区", "salary": "28K-50K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉监控, <PERSON><PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 远程办公, 健身房, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298296", "job_id": "cfa1562a5d2825a3"}, {"title": "算法工程师", "company": "博彦科技", "location": "昆明-高新区", "salary": "25K-45K", "education": "博士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 博士及以上学历; 熟悉机器学习, 深度学习, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 年终奖, 绩效奖金, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298436", "job_id": "666f27b1d14a36c8"}, {"title": "产品经理", "company": "文思海辉", "location": "太原-市中心", "salary": "35K-60K", "education": "硕士", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉用户研究, 数据分析, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 免费午餐, 补充医疗保险, 带薪年假, 五险一金, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298571", "job_id": "059a77ca3f388af2"}, {"title": "算法工程师", "company": "东软集团", "location": "上海-徐汇区", "salary": "25K-45K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON><PERSON>, 深度学习, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 团建活动, 通讯补贴, 交通补贴, 住房补贴, 年终奖, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298708", "job_id": "9b1e89ba29120560"}, {"title": "算法工程师", "company": "华胜天成", "location": "石家庄-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉Tensor<PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 免费午餐, 远程办公, 团建活动, 培训机会, 股票期权, 补充医疗保险, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298840", "job_id": "4989c3ef541b69be"}, {"title": "算法工程师", "company": "文思海辉", "location": "厦门-市中心", "salary": "25K-45K", "education": "硕士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉Tensor<PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, 机器学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 弹性工作, 通讯补贴, 远程办公, 五险一金, 免费班车, 下午茶, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.298985", "job_id": "bd60653b00c1fca6"}, {"title": "DevOps工程师", "company": "明略科技", "location": "昆明-市中心", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>er, 监控; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 补充医疗保险, 免费班车, 健身房, 五险一金, 带薪年假, 交通补贴, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299110", "job_id": "3c2bd9452ff9b012"}, {"title": "算法工程师", "company": "华胜天成", "location": "石家庄-市中心", "salary": "25K-45K", "education": "硕士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉算法优化, <PERSON>y<PERSON><PERSON><PERSON>, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 健身房, 下午茶, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299242", "job_id": "25cdf8c715644209"}, {"title": "DevOps工程师", "company": "中软国际", "location": "青岛-高新区", "salary": "20K-35K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON>, <PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 带薪年假, 补充医疗保险, 住房补贴, 培训机会, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299370", "job_id": "4ce7d677a7283d13"}, {"title": "产品经理", "company": "软通动力", "location": "郑州-高新区", "salary": "25K-45K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉数据分析, 项目管理, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 五险一金, 团建活动, 股票期权, 下午茶, 健身房, 免费午餐, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299494", "job_id": "88f923c64852f042"}, {"title": "产品经理", "company": "紫光集团", "location": "大连-经济开发区", "salary": "25K-45K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉用户研究, 项目管理, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 交通补贴, 培训机会, 免费班车, 技术津贴, 五险一金, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299620", "job_id": "f741c7a332f80189"}, {"title": "产品经理", "company": "云从科技", "location": "太原-市中心", "salary": "25K-45K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉数据分析, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 年终奖, 交通补贴, 五险一金, 绩效奖金, 健身房, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299745", "job_id": "18219a687c661ac8"}, {"title": "产品经理", "company": "软通动力", "location": "沈阳-市中心", "salary": "35K-60K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉用户研究, 数据分析, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 带薪年假, 五险一金, 股票期权, 弹性工作, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.299868", "job_id": "fbaa44f67a7b982a"}, {"title": "算法工程师", "company": "依图科技", "location": "南京-高新区", "salary": "25K-45K", "education": "硕士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 硕士及以上学历; 熟悉算法优化, TensorFlow, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 培训机会, 补充医疗保险, 免费班车, 通讯补贴, 五险一金, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300007", "job_id": "ff45427dfad62eaa"}, {"title": "产品经理", "company": "旷视科技", "location": "沈阳-高新区", "salary": "35K-60K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉项目管理, 数据分析, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 住房补贴, 下午茶, 通讯补贴, 弹性工作, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300133", "job_id": "7d712c075a5147d6"}, {"title": "算法工程师", "company": "第四范式", "location": "北京-朝阳区", "salary": "25K-45K", "education": "硕士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 硕士及以上学历; 熟悉算法优化, TensorFlow, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 交通补贴, 团建活动, 绩效奖金, 股票期权, 远程办公, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300266", "job_id": "208719781cbab96e"}, {"title": "产品经理", "company": "紫光集团", "location": "苏州-市中心", "salary": "25K-45K", "education": "硕士", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 硕士及以上学历; 熟悉数据分析, 项目管理, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 下午茶, 住房补贴, 免费午餐, 股票期权, 免费班车, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300405", "job_id": "41fa41617f0d6e34"}, {"title": "算法工程师", "company": "东软集团", "location": "石家庄-市中心", "salary": "45K-80K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉算法优化, TensorFlow, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 团建活动, 通讯补贴, 五险一金, 远程办公, 交通补贴, 股票期权, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300609", "job_id": "3953b01fd63c069e"}, {"title": "算法工程师", "company": "紫光集团", "location": "武汉-高新区", "salary": "45K-80K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉深度学习, TensorFlow, 算法优化; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 绩效奖金, 健身房, 免费午餐, 技术津贴, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300767", "job_id": "8876de0f5da67540"}, {"title": "算法工程师", "company": "明略科技", "location": "太原-市中心", "salary": "45K-80K", "education": "硕士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 硕士及以上学历; 熟悉TensorFlow, 机器学习, 算法优化; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 免费午餐, 弹性工作, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.300902", "job_id": "fa8c81d51f7f068a"}, {"title": "算法工程师", "company": "大华股份", "location": "大连-高新区", "salary": "25K-45K", "education": "博士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 博士及以上学历; 熟悉机器学习, 算法优化, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 免费午餐, 住房补贴, 五险一金, 远程办公, 交通补贴, 健身房, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301052", "job_id": "5d31fcf0579453ee"}, {"title": "算法工程师", "company": "海康威视", "location": "天津-高新区", "salary": "45K-80K", "education": "硕士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉算法优化, 机器学习, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 免费午餐, 培训机会, 五险一金, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301183", "job_id": "8aad4b98f3c9e5b1"}, {"title": "产品经理", "company": "旷视科技", "location": "深圳-罗湖区", "salary": "25K-45K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉用户研究, 项目管理, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 通讯补贴, 团建活动, 住房补贴, 免费班车, 弹性工作, 绩效奖金, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301322", "job_id": "2305d7244bce61a5"}, {"title": "产品经理", "company": "第四范式", "location": "大连-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉项目管理, 产品设计, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 远程办公, 团建活动, 住房补贴, 培训机会, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301455", "job_id": "06e33e1bc24aa588"}, {"title": "产品经理", "company": "依图科技", "location": "苏州-高新区", "salary": "35K-60K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉产品设计, 项目管理, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 补充医疗保险, 团建活动, 带薪年假, 免费午餐, 技术津贴, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301582", "job_id": "43c5c4734b525e00"}, {"title": "产品经理", "company": "海康威视", "location": "南京-市中心", "salary": "35K-60K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉项目管理, 用户研究, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 下午茶, 交通补贴, 团建活动, 技术津贴, 免费班车, 住房补贴, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301712", "job_id": "cf911361f9ed7795"}, {"title": "DevOps工程师", "company": "紫光集团", "location": "杭州-高新区", "salary": "20K-35K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉自动化, <PERSON>, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 健身房, 技术津贴, 补充医疗保险, 免费午餐, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301844", "job_id": "cf570b678c45b370"}, {"title": "DevOps工程师", "company": "商汤科技", "location": "成都-高新区", "salary": "35K-60K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉Kubernetes, 监控, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 住房补贴, 股票期权, 免费班车, 下午茶, 带薪年假, 技术津贴, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.301980", "job_id": "533a2a318bd319e4"}, {"title": "DevOps工程师", "company": "云从科技", "location": "青岛-高新区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>er, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 团建活动, 股票期权, 远程办公, 培训机会, 带薪年假, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302110", "job_id": "e58753177fb62e4a"}, {"title": "DevOps工程师", "company": "软通动力", "location": "昆明-经济开发区", "salary": "28K-50K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, 监控, <PERSON>er; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 带薪年假, 下午茶, 团建活动, 绩效奖金, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302242", "job_id": "e2b01d742e6c6221"}, {"title": "DevOps工程师", "company": "文思海辉", "location": "成都-经济开发区", "salary": "35K-60K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, 监控, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 年终奖, 通讯补贴, 免费午餐, 补充医疗保险, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302383", "job_id": "cd781e2b7cd1767c"}, {"title": "DevOps工程师", "company": "紫光集团", "location": "北京-海淀区", "salary": "35K-60K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉<PERSON>, <PERSON>er, 监控; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 带薪年假, 团建活动, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302513", "job_id": "594e74ba07eed8c1"}, {"title": "产品经理", "company": "第四范式", "location": "合肥-经济开发区", "salary": "18K-30K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉用户研究, 项目管理, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 免费班车, 补充医疗保险, 培训机会, 股票期权, 住房补贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302642", "job_id": "90078705ee87cda7"}, {"title": "DevOps工程师", "company": "文思海辉", "location": "广州-白云区", "salary": "20K-35K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉自动化, <PERSON><PERSON><PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 培训机会, 带薪年假, 股票期权, 免费班车, 通讯补贴, 绩效奖金, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302783", "job_id": "3d209fe22a9438da"}, {"title": "DevOps工程师", "company": "第四范式", "location": "北京-西城区", "salary": "20K-35K", "education": "本科", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 本科及以上学历; 熟悉Docker, 监控, <PERSON><PERSON><PERSON><PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 住房补贴, 交通补贴, 弹性工作, 补充医疗保险, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.302918", "job_id": "3b7e3366a4a7cc7e"}, {"title": "DevOps工程师", "company": "第四范式", "location": "沈阳-市中心", "salary": "28K-50K", "education": "硕士", "experience": "8-12年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "8-12年工作经验; 硕士及以上学历; 熟悉<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, 自动化; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 通讯补贴, 补充医疗保险, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303048", "job_id": "769504e5f62c1d31"}, {"title": "DevOps工程师", "company": "紫光集团", "location": "杭州-市中心", "salary": "28K-50K", "education": "硕士", "experience": "5-8年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "5-8年工作经验; 硕士及以上学历; 熟悉<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Docker; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 绩效奖金, 免费午餐, 团建活动, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303184", "job_id": "573ed0b828529950"}, {"title": "产品经理", "company": "中软国际", "location": "厦门-经济开发区", "salary": "25K-45K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉项目管理, 用户研究, 产品设计; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 健身房, 下午茶, 培训机会, 交通补贴, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303337", "job_id": "98964a8de8db5015"}, {"title": "算法工程师", "company": "中软国际", "location": "苏州-高新区", "salary": "25K-45K", "education": "博士", "experience": "5-10年", "description": "负责AI算法研发，模型训练和优化", "requirements": "5-10年工作经验; 博士及以上学历; 熟悉TensorFlow, 深度学习, 机器学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 健身房, 免费班车, 年终奖, 补充医疗保险, 通讯补贴, 五险一金, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303481", "job_id": "152c744ee0d4ed12"}, {"title": "产品经理", "company": "明略科技", "location": "厦门-市中心", "salary": "25K-45K", "education": "本科", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉产品设计, 数据分析, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 培训机会, 下午茶, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303613", "job_id": "af9ddf7a8fe5ecca"}, {"title": "产品经理", "company": "明略科技", "location": "西安-经济开发区", "salary": "18K-30K", "education": "本科", "experience": "5-12年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉数据分析, 用户研究, 项目管理; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 技术津贴, 健身房, 通讯补贴, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303751", "job_id": "c0a69981ef10282f"}, {"title": "DevOps工程师", "company": "大华股份", "location": "深圳-福田区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责CI/CD流程建设，系统运维自动化", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉监控, <PERSON><PERSON>, <PERSON>; 熟悉容器化技术，有运维自动化经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 五险一金, 绩效奖金, 团建活动, 通讯补贴, 免费午餐, 健身房, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.303885", "job_id": "f82b958a78109d5b"}, {"title": "算法工程师", "company": "商汤科技", "location": "南京-经济开发区", "salary": "25K-45K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉机器学习, <PERSON>y<PERSON><PERSON><PERSON>, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 五险一金, 带薪年假, 补充医疗保险, 年终奖, 团建活动, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304044", "job_id": "520aa11195cfeadc"}, {"title": "产品经理", "company": "海康威视", "location": "西安-经济开发区", "salary": "35K-60K", "education": "硕士", "experience": "3-8年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "3-8年工作经验; 硕士及以上学历; 熟悉用户研究, 产品设计, 数据分析; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 五险一金, 健身房, 年终奖, 技术津贴, 带薪年假, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304178", "job_id": "c4761daeb16068f1"}, {"title": "算法工程师", "company": "海康威视", "location": "西安-高新区", "salary": "35K-60K", "education": "硕士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉算法优化, <PERSON>y<PERSON><PERSON><PERSON>, TensorFlow; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 股票期权, 团建活动, 年终奖, 补充医疗保险, 培训机会, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304309", "job_id": "7af3e56e0db422a8"}, {"title": "产品经理", "company": "东软集团", "location": "深圳-宝安区", "salary": "25K-45K", "education": "硕士", "experience": "2-5年", "description": "负责产品规划设计，需求分析和项目推进", "requirements": "2-5年工作经验; 硕士及以上学历; 熟悉数据分析, 项目管理, 用户研究; 有互联网产品经验，熟悉用户体验设计; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 补充医疗保险, 通讯补贴, 交通补贴, 弹性工作, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304434", "job_id": "384b39f4a9b21909"}, {"title": "算法工程师", "company": "云从科技", "location": "昆明-高新区", "salary": "25K-45K", "education": "博士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 博士及以上学历; 熟悉PyT<PERSON><PERSON>, 算法优化, 深度学习; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 年终奖, 弹性工作, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304566", "job_id": "54c320dab9f9e8eb"}, {"title": "算法工程师", "company": "汇顶科技", "location": "合肥-高新区", "salary": "25K-45K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉机器学习, 深度学习, 算法优化; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 绩效奖金, 免费午餐, 股票期权, 带薪年假, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304691", "job_id": "415cd5379ad9776e"}, {"title": "算法工程师", "company": "华胜天成", "location": "杭州-市中心", "salary": "25K-45K", "education": "硕士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 硕士及以上学历; 熟悉深度学习, 机器学习, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 交通补贴, 培训机会, 年终奖, 五险一金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304820", "job_id": "b71cd3f7ab70d54e"}, {"title": "算法工程师", "company": "第四范式", "location": "成都-高新区", "salary": "25K-45K", "education": "博士", "experience": "8-15年", "description": "负责AI算法研发，模型训练和优化", "requirements": "8-15年工作经验; 博士及以上学历; 熟悉机器学习, 算法优化, PyTorch; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 免费班车, 绩效奖金, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.304975", "job_id": "4f7ae732bb3313c9"}, {"title": "算法工程师", "company": "依图科技", "location": "上海-徐汇区", "salary": "45K-80K", "education": "硕士", "experience": "3-6年", "description": "负责AI算法研发，模型训练和优化", "requirements": "3-6年工作经验; 硕士及以上学历; 熟悉Tensor<PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, 算法优化; 有机器学习项目经验，熟悉深度学习框架; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 绩效奖金, 弹性工作, 补充医疗保险, 通讯补贴, 下午茶, 住房补贴, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305107", "job_id": "98a07026d66cacb0"}, {"title": "软件测试工程师", "company": "金蝶软件", "location": "青岛-经济开发区", "salary": "12K-20K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化测试, 接口测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 补充医疗保险, 带薪年假, 技术津贴, 住房补贴, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305234", "job_id": "fae7da895616eb80"}, {"title": "运维工程师", "company": "启明星辰", "location": "南京-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Linux, Shell, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 补充医疗保险, 五险一金, 住房补贴, 培训机会, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305378", "job_id": "219070dc2be38cdc"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "成都-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉性能测试, 自动化测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 免费班车, 团建活动, 健身房, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305525", "job_id": "d8b8730c729efbf1"}, {"title": "运维工程师", "company": "苏宁易购", "location": "厦门-经济开发区", "salary": "20K-35K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉数据库, Shell, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 年终奖, 带薪年假, 培训机会, 团建活动, 免费午餐, 下午茶, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305669", "job_id": "b7cc5cc3d5da8ba6"}, {"title": "软件测试工程师", "company": "金蝶软件", "location": "福州-高新区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化测试, 接口测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 住房补贴, 团建活动, 带薪年假, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305799", "job_id": "0b5a3e8bc0490889"}, {"title": "运维工程师", "company": "货拉拉", "location": "成都-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉数据库, Linux, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 免费班车, 年终奖, 五险一金, 健身房, 弹性工作, 绩效奖金, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.305929", "job_id": "43fc12d2d2a959ae"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "武汉-高新区", "salary": "15K-25K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉自动化测试, 接口测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 团建活动, 住房补贴, 免费班车, 股票期权, 远程办公, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306076", "job_id": "013960d11259883f"}, {"title": "运维工程师", "company": "苏宁易购", "location": "福州-经济开发区", "salary": "20K-35K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉Shell, 数据库, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 绩效奖金, 股票期权, 交通补贴, 下午茶, 技术津贴, 通讯补贴, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306203", "job_id": "31d8d622f5bfd715"}, {"title": "软件测试工程师", "company": "唯品会", "location": "苏州-高新区", "salary": "15K-25K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉测试框架, 性能测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 下午茶, 免费班车, 年终奖, 五险一金, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306328", "job_id": "34a32586ee369660"}, {"title": "软件测试工程师", "company": "货拉拉", "location": "南京-市中心", "salary": "20K-35K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉测试框架, 自动化测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 免费班车, 股票期权, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306467", "job_id": "4ccb192ca5c29dd5"}, {"title": "软件测试工程师", "company": "哈啰出行", "location": "沈阳-高新区", "salary": "15K-25K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉自动化测试, 接口测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 健身房, 团建活动, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306600", "job_id": "c447986dfdaab793"}, {"title": "运维工程师", "company": "金蝶软件", "location": "济南-高新区", "salary": "15K-25K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉数据库, Linux, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 远程办公, 弹性工作, 年终奖, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306765", "job_id": "d50b7cc5f4e9b9a2"}, {"title": "软件测试工程师", "company": "用友网络", "location": "大连-高新区", "salary": "12K-20K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化测试, 性能测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 团建活动, 健身房, 培训机会, 技术津贴, 带薪年假, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.306979", "job_id": "574309ec1b886544"}, {"title": "运维工程师", "company": "奇安信", "location": "深圳-罗湖区", "salary": "28K-50K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉网络, 监控, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 补充医疗保险, 交通补贴, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.307208", "job_id": "f028caea1b6b369e"}, {"title": "软件测试工程师", "company": "顺丰科技", "location": "太原-经济开发区", "salary": "20K-35K", "education": "大专", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 大专及以上学历; 熟悉性能测试, 接口测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 五险一金, 弹性工作, 住房补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.307359", "job_id": "9887c8ba9c54fc77"}, {"title": "运维工程师", "company": "拼多多", "location": "西安-市中心", "salary": "28K-50K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉网络, Linux, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 五险一金, 培训机会, 下午茶, 住房补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.307563", "job_id": "3a664ab34b5e2bfd"}, {"title": "运维工程师", "company": "货拉拉", "location": "合肥-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉网络, 数据库, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 年终奖, 技术津贴, 免费午餐, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.307748", "job_id": "32b968c38951e28b"}, {"title": "运维工程师", "company": "货拉拉", "location": "南京-高新区", "salary": "20K-35K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉Shell, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 补充医疗保险, 远程办公, 健身房, 五险一金, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.307900", "job_id": "2565902a0fc44763"}, {"title": "软件测试工程师", "company": "哈啰出行", "location": "广州-海珠区", "salary": "12K-20K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉接口测试, 性能测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 股票期权, 交通补贴, 远程办公, 绩效奖金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.308165", "job_id": "b9dafaa0ff2fbe80"}, {"title": "运维工程师", "company": "当当网", "location": "石家庄-经济开发区", "salary": "15K-25K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉数据库, Shell, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 培训机会, 弹性工作, 年终奖, 团建活动, 交通补贴, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.308356", "job_id": "2213477fc3ac89a1"}, {"title": "软件测试工程师", "company": "货拉拉", "location": "合肥-市中心", "salary": "12K-20K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉接口测试, 自动化测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 技术津贴, 团建活动, 绩效奖金, 下午茶, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.308507", "job_id": "9e59d24d97194d9d"}, {"title": "运维工程师", "company": "用友网络", "location": "济南-高新区", "salary": "28K-50K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉数据库, 网络, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 免费午餐, 年终奖, 健身房, 下午茶, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.308662", "job_id": "0923a2b3531cd14a"}, {"title": "运维工程师", "company": "聚美优品", "location": "济南-市中心", "salary": "15K-25K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉Shell, Linux, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 团建活动, 弹性工作, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.308809", "job_id": "bb683132b753d36c"}, {"title": "运维工程师", "company": "当当网", "location": "上海-长宁区", "salary": "28K-50K", "education": "大专", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 大专及以上学历; 熟悉Linux, 监控, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 下午茶, 技术津贴, 补充医疗保险, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.308952", "job_id": "00a3cccdbfbfe9a2"}, {"title": "软件测试工程师", "company": "唯品会", "location": "杭州-高新区", "salary": "15K-25K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉接口测试, 性能测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 住房补贴, 年终奖, 培训机会, 免费班车, 带薪年假, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.309157", "job_id": "1bb5cc0a2deb0a55"}, {"title": "软件测试工程师", "company": "满帮集团", "location": "北京-东城区", "salary": "15K-25K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉接口测试, 性能测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 免费班车, 绩效奖金, 弹性工作, 补充医疗保险, 技术津贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.309316", "job_id": "fa8ad960d27b51d4"}, {"title": "运维工程师", "company": "菜鸟网络", "location": "武汉-高新区", "salary": "20K-35K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉数据库, Shell, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 带薪年假, 技术津贴, 健身房, 交通补贴, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.309471", "job_id": "fb3b457fcb65a880"}, {"title": "软件测试工程师", "company": "拼多多", "location": "昆明-市中心", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉测试框架, 接口测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 五险一金, 团建活动, 弹性工作, 免费班车, 年终奖, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.309617", "job_id": "a19613dbb0c02422"}, {"title": "运维工程师", "company": "唯品会", "location": "昆明-市中心", "salary": "28K-50K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉监控, Linux, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 远程办公, 股票期权, 团建活动, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.309760", "job_id": "a0f5287b7b31a6d0"}, {"title": "软件测试工程师", "company": "金蝶软件", "location": "石家庄-市中心", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉性能测试, 接口测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 健身房, 绩效奖金, 免费午餐, 交通补贴, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.309915", "job_id": "13f114e9846dbc66"}, {"title": "运维工程师", "company": "当当网", "location": "福州-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉监控, Shell, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 健身房, 下午茶, 补充医疗保险, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.310063", "job_id": "284b34a429e86d46"}, {"title": "运维工程师", "company": "拼多多", "location": "昆明-高新区", "salary": "20K-35K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉Linux, 网络, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 通讯补贴, 五险一金, 免费班车, 技术津贴, 弹性工作, 住房补贴, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.310201", "job_id": "37c6b3f06811ca20"}, {"title": "软件测试工程师", "company": "国美在线", "location": "重庆-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉接口测试, 性能测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 团建活动, 下午茶, 免费午餐, 带薪年假, 培训机会, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.310340", "job_id": "cdb15289f259358f"}, {"title": "运维工程师", "company": "用友网络", "location": "郑州-市中心", "salary": "28K-50K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Linux, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 培训机会, 年终奖, 远程办公, 团建活动, 住房补贴, 下午茶, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.310514", "job_id": "d9135dd969fadbde"}, {"title": "运维工程师", "company": "当当网", "location": "福州-市中心", "salary": "15K-25K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉数据库, 监控, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 带薪年假, 年终奖, 补充医疗保险, 健身房, 住房补贴, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.310706", "job_id": "78d591819528e038"}, {"title": "运维工程师", "company": "唯品会", "location": "西安-高新区", "salary": "28K-50K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Shell, 数据库, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 技术津贴, 团建活动, 股票期权, 远程办公, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.310885", "job_id": "13d1f12cb0185724"}, {"title": "运维工程师", "company": "奇安信", "location": "北京-西城区", "salary": "20K-35K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉Shell, 网络, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 交通补贴, 免费班车, 下午茶, 远程办公, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.311049", "job_id": "76d4012238f6cd37"}, {"title": "软件测试工程师", "company": "国美在线", "location": "大连-高新区", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化测试, 测试框架, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 股票期权, 下午茶, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.311198", "job_id": "4a7f18c01cd0920f"}, {"title": "运维工程师", "company": "苏宁易购", "location": "青岛-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Linux, 网络, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 团建活动, 五险一金, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.311376", "job_id": "2a13b4c2589f2e7a"}, {"title": "软件测试工程师", "company": "用友网络", "location": "北京-海淀区", "salary": "20K-35K", "education": "大专", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 大专及以上学历; 熟悉接口测试, 自动化测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 绩效奖金, 免费班车, 培训机会, 住房补贴, 技术津贴, 补充医疗保险, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.311541", "job_id": "06a6a3abb3cbec2f"}, {"title": "软件测试工程师", "company": "货拉拉", "location": "苏州-市中心", "salary": "15K-25K", "education": "大专", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 大专及以上学历; 熟悉自动化测试, 测试框架, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 五险一金, 技术津贴, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.311679", "job_id": "2dc6bd56e392aa9d"}, {"title": "软件测试工程师", "company": "用友网络", "location": "大连-经济开发区", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉接口测试, 测试框架, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 免费午餐, 五险一金, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.311832", "job_id": "230e0b114d73f76e"}, {"title": "运维工程师", "company": "启明星辰", "location": "苏州-市中心", "salary": "15K-25K", "education": "本科", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Shell, Linux, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 下午茶, 培训机会, 团建活动, 补充医疗保险, 五险一金, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.312000", "job_id": "056e452b88dabd2a"}, {"title": "运维工程师", "company": "唯品会", "location": "郑州-高新区", "salary": "28K-50K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉网络, Shell, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 弹性工作, 技术津贴, 住房补贴, 年终奖, 股票期权, 免费班车, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.312156", "job_id": "3b761beff7feee7e"}, {"title": "运维工程师", "company": "满帮集团", "location": "广州-天河区", "salary": "20K-35K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉网络, Linux, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 五险一金, 住房补贴, 通讯补贴, 技术津贴, 下午茶, 交通补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.312304", "job_id": "486b0c5a7561358b"}, {"title": "运维工程师", "company": "当当网", "location": "厦门-市中心", "salary": "20K-35K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉网络, 监控, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 五险一金, 年终奖, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.312455", "job_id": "5fd310296f80ac3f"}, {"title": "运维工程师", "company": "满帮集团", "location": "合肥-经济开发区", "salary": "28K-50K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Linux, Shell, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 免费班车, 弹性工作, 股票期权, 带薪年假, 补充医疗保险, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.312714", "job_id": "27141ee8c583b443"}, {"title": "软件测试工程师", "company": "启明星辰", "location": "南京-高新区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉测试框架, 自动化测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 通讯补贴, 带薪年假, 五险一金, 补充医疗保险, 健身房, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.312883", "job_id": "bc92b41e930e6dee"}, {"title": "软件测试工程师", "company": "用友网络", "location": "郑州-高新区", "salary": "15K-25K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉性能测试, 自动化测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 股票期权, 年终奖, 补充医疗保险, 培训机会, 五险一金, 住房补贴, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313040", "job_id": "897f09ea1eff9233"}, {"title": "运维工程师", "company": "当当网", "location": "天津-市中心", "salary": "20K-35K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉监控, Linux, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 远程办公, 健身房, 下午茶, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313184", "job_id": "4af936c77d5a4806"}, {"title": "软件测试工程师", "company": "金蝶软件", "location": "苏州-高新区", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉测试框架, 接口测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 免费班车, 技术津贴, 补充医疗保险, 绩效奖金, 远程办公, 健身房, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313335", "job_id": "21ea3b7fb88b7d8a"}, {"title": "运维工程师", "company": "金蝶软件", "location": "长沙-市中心", "salary": "28K-50K", "education": "本科", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉监控, Linux, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 绩效奖金, 交通补贴, 补充医疗保险, 住房补贴, 健身房, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313489", "job_id": "4493df95fe6bb69b"}, {"title": "运维工程师", "company": "聚美优品", "location": "福州-经济开发区", "salary": "15K-25K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉监控, 网络, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 通讯补贴, 补充医疗保险, 免费班车, 培训机会, 带薪年假, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313636", "job_id": "9c7c7e7a75c5f32d"}, {"title": "运维工程师", "company": "聚美优品", "location": "石家庄-高新区", "salary": "15K-25K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Shell, 网络, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 下午茶, 团建活动, 股票期权, 五险一金, 健身房, 年终奖, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313769", "job_id": "7fa5ed19afc6155b"}, {"title": "软件测试工程师", "company": "拼多多", "location": "长沙-市中心", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉接口测试, 自动化测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 下午茶, 健身房, 培训机会, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.313896", "job_id": "872723243b31aa84"}, {"title": "软件测试工程师", "company": "唯品会", "location": "西安-高新区", "salary": "20K-35K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉接口测试, 测试框架, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 补充医疗保险, 弹性工作, 健身房, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314029", "job_id": "d7e9903cee42c90e"}, {"title": "运维工程师", "company": "菜鸟网络", "location": "北京-海淀区", "salary": "15K-25K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉数据库, 网络, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 健身房, 补充医疗保险, 股票期权, 五险一金, 团建活动, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314205", "job_id": "cf7134a11d2d6b58"}, {"title": "运维工程师", "company": "拼多多", "location": "天津-高新区", "salary": "20K-35K", "education": "大专", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 大专及以上学历; 熟悉Shell, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 远程办公, 下午茶, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314354", "job_id": "39d12cdebff14ae6"}, {"title": "运维工程师", "company": "顺丰科技", "location": "上海-静安区", "salary": "28K-50K", "education": "本科", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉网络, Linux, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 技术津贴, 住房补贴, 团建活动, 带薪年假, 弹性工作, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314487", "job_id": "8cdc11ee3c76529b"}, {"title": "软件测试工程师", "company": "奇安信", "location": "昆明-市中心", "salary": "20K-35K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉接口测试, 测试框架, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 免费午餐, 股票期权, 住房补贴, 健身房, 技术津贴, 带薪年假, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314625", "job_id": "73243b322ace7a5a"}, {"title": "运维工程师", "company": "拼多多", "location": "石家庄-高新区", "salary": "15K-25K", "education": "大专", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 大专及以上学历; 熟悉Linux, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 住房补贴, 免费班车, 绩效奖金, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314756", "job_id": "62ad113595f349e2"}, {"title": "软件测试工程师", "company": "顺丰科技", "location": "北京-朝阳区", "salary": "12K-20K", "education": "大专", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 大专及以上学历; 熟悉接口测试, 测试框架, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 带薪年假, 团建活动, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.314890", "job_id": "bee9fdd51c2f103a"}, {"title": "运维工程师", "company": "货拉拉", "location": "济南-高新区", "salary": "15K-25K", "education": "大专", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 大专及以上学历; 熟悉网络, Linux, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 年终奖, 交通补贴, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315038", "job_id": "e643879b708d2683"}, {"title": "软件测试工程师", "company": "当当网", "location": "武汉-高新区", "salary": "12K-20K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉自动化测试, 性能测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 补充医疗保险, 五险一金, 通讯补贴, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315180", "job_id": "2c85637765bde79b"}, {"title": "运维工程师", "company": "唯品会", "location": "深圳-南山区", "salary": "20K-35K", "education": "大专", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 大专及以上学历; 熟悉网络, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 五险一金, 绩效奖金, 弹性工作, 下午茶, 通讯补贴, 技术津贴, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315314", "job_id": "f17e8a222749732e"}, {"title": "软件测试工程师", "company": "苏宁易购", "location": "合肥-高新区", "salary": "15K-25K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉性能测试, 接口测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 免费班车, 绩效奖金, 住房补贴, 交通补贴, 下午茶", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315442", "job_id": "9af420294b3c17a4"}, {"title": "运维工程师", "company": "绿盟科技", "location": "郑州-高新区", "salary": "15K-25K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉数据库, 监控, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 培训机会, 健身房, 远程办公, 免费班车, 补充医疗保险, 团建活动, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315571", "job_id": "2505e082cefce909"}, {"title": "运维工程师", "company": "拼多多", "location": "成都-高新区", "salary": "20K-35K", "education": "大专", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 大专及以上学历; 熟悉网络, 监控, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 培训机会, 远程办公, 通讯补贴, 带薪年假, 下午茶, 住房补贴, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315697", "job_id": "f1d911fbd3e533c9"}, {"title": "软件测试工程师", "company": "奇安信", "location": "上海-徐汇区", "salary": "15K-25K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉接口测试, 自动化测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 五险一金, 健身房, 远程办公, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315828", "job_id": "dde077e65516d7f3"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "济南-高新区", "salary": "12K-20K", "education": "大专", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 大专及以上学历; 熟悉测试框架, 接口测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费班车, 技术津贴, 带薪年假, 通讯补贴, 五险一金, 健身房, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.315969", "job_id": "6934d7e458524ab7"}, {"title": "软件测试工程师", "company": "聚美优品", "location": "武汉-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉测试框架, 性能测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "年终奖, 住房补贴, 远程办公, 下午茶, 免费午餐, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316093", "job_id": "363d0224c5b1d544"}, {"title": "运维工程师", "company": "奇安信", "location": "天津-市中心", "salary": "28K-50K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉数据库, Linux, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 绩效奖金, 免费班车, 团建活动, 年终奖, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316217", "job_id": "cde7154d6a7c268e"}, {"title": "软件测试工程师", "company": "拼多多", "location": "苏州-高新区", "salary": "12K-20K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉自动化测试, 性能测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 补充医疗保险, 带薪年假, 股票期权, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316349", "job_id": "78ff157afe2e19a5"}, {"title": "软件测试工程师", "company": "启明星辰", "location": "南京-经济开发区", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉接口测试, 性能测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 股票期权, 技术津贴, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316476", "job_id": "d647394049e41a57"}, {"title": "运维工程师", "company": "拼多多", "location": "福州-经济开发区", "salary": "28K-50K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Shell, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 远程办公, 五险一金, 交通补贴, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316605", "job_id": "550f4259f74dc384"}, {"title": "软件测试工程师", "company": "货拉拉", "location": "杭州-高新区", "salary": "15K-25K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉测试框架, 性能测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 弹性工作, 远程办公, 补充医疗保险, 股票期权, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316725", "job_id": "ef61e53c772f2f16"}, {"title": "软件测试工程师", "company": "国美在线", "location": "福州-经济开发区", "salary": "12K-20K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉测试框架, 接口测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 五险一金, 绩效奖金, 健身房", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316862", "job_id": "9c9fbcee40e2aca1"}, {"title": "软件测试工程师", "company": "苏宁易购", "location": "成都-市中心", "salary": "20K-35K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉性能测试, 自动化测试, 接口测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "补充医疗保险, 下午茶, 团建活动, 交通补贴, 免费午餐, 五险一金, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.316984", "job_id": "1276bf165b5e5c3a"}, {"title": "运维工程师", "company": "国美在线", "location": "成都-高新区", "salary": "15K-25K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉网络, Shell, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 股票期权, 团建活动, 带薪年假, 年终奖", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317103", "job_id": "f9dabf9b7b05c7ca"}, {"title": "运维工程师", "company": "货拉拉", "location": "天津-高新区", "salary": "28K-50K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉网络, Shell, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 远程办公, 免费午餐, 股票期权, 免费班车, 年终奖, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317230", "job_id": "3af675bae4dd6408"}, {"title": "运维工程师", "company": "绿盟科技", "location": "济南-高新区", "salary": "28K-50K", "education": "本科", "experience": "3-8年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "3-8年工作经验; 本科及以上学历; 熟悉Linux, 网络, Shell; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 技术津贴, 绩效奖金, 弹性工作, 通讯补贴, 远程办公", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317401", "job_id": "2cabafb6d5a7546c"}, {"title": "软件测试工程师", "company": "货拉拉", "location": "北京-朝阳区", "salary": "20K-35K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉测试框架, 性能测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 五险一金, 年终奖, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317568", "job_id": "4576d0547f521abf"}, {"title": "软件测试工程师", "company": "货拉拉", "location": "沈阳-高新区", "salary": "15K-25K", "education": "大专", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 大专及以上学历; 熟悉测试框架, 性能测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "团建活动, 五险一金, 年终奖, 免费午餐, 股票期权, 带薪年假, 免费班车, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317709", "job_id": "2cd853aedfbad13e"}, {"title": "运维工程师", "company": "菜鸟网络", "location": "厦门-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉数据库, Shell, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 下午茶, 免费午餐, 培训机会, 技术津贴, 远程办公, 股票期权", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317863", "job_id": "dd4ef417cb030698"}, {"title": "软件测试工程师", "company": "聚美优品", "location": "沈阳-高新区", "salary": "15K-25K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉接口测试, 测试框架, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 技术津贴, 团建活动, 年终奖, 通讯补贴, 绩效奖金, 远程办公, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.317998", "job_id": "0d588f4f65214c33"}, {"title": "软件测试工程师", "company": "哈啰出行", "location": "大连-市中心", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉测试框架, 性能测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 下午茶, 免费午餐, 健身房, 五险一金, 技术津贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318125", "job_id": "6486c25d8c7ac9c0"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "北京-西城区", "salary": "15K-25K", "education": "大专", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 大专及以上学历; 熟悉接口测试, 测试框架, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "免费午餐, 团建活动, 五险一金, 绩效奖金, 通讯补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318246", "job_id": "73883a1b759135a7"}, {"title": "运维工程师", "company": "金蝶软件", "location": "深圳-宝安区", "salary": "15K-25K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉监控, Shell, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "下午茶, 年终奖, 交通补贴, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318363", "job_id": "7cc42f160fcd99de"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "合肥-经济开发区", "salary": "12K-20K", "education": "大专", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 大专及以上学历; 熟悉接口测试, 测试框架, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 绩效奖金, 弹性工作, 免费午餐, 健身房, 带薪年假, 五险一金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318485", "job_id": "bc4138bcb82042dd"}, {"title": "运维工程师", "company": "金蝶软件", "location": "青岛-市中心", "salary": "20K-35K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉监控, Shell, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 下午茶, 远程办公, 绩效奖金, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318604", "job_id": "0dc49327b3c2abf0"}, {"title": "运维工程师", "company": "哈啰出行", "location": "武汉-经济开发区", "salary": "20K-35K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉网络, 监控, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 团建活动, 通讯补贴, 绩效奖金, 交通补贴", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318720", "job_id": "cb111a88d806bad9"}, {"title": "软件测试工程师", "company": "唯品会", "location": "沈阳-高新区", "salary": "15K-25K", "education": "本科", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 本科及以上学历; 熟悉测试框架, 性能测试, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 住房补贴, 健身房, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318837", "job_id": "67d682393c5a916f"}, {"title": "运维工程师", "company": "聚美优品", "location": "武汉-经济开发区", "salary": "15K-25K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉Shell, 数据库, Linux; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 交通补贴, 免费班车, 远程办公, 通讯补贴, 绩效奖金, 培训机会, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.318959", "job_id": "a4d5b33ffb32b062"}, {"title": "运维工程师", "company": "绿盟科技", "location": "广州-天河区", "salary": "20K-35K", "education": "本科", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 本科及以上学历; 熟悉网络, 监控, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 培训机会, 下午茶, 交通补贴, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319079", "job_id": "47ee12bebb76c470"}, {"title": "运维工程师", "company": "用友网络", "location": "南京-市中心", "salary": "15K-25K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉Linux, 数据库, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "交通补贴, 团建活动, 培训机会, 股票期权, 弹性工作, 五险一金, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319204", "job_id": "b807c1b2dd3c468e"}, {"title": "运维工程师", "company": "唯品会", "location": "郑州-市中心", "salary": "28K-50K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉网络, Linux, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "弹性工作, 交通补贴, 带薪年假, 年终奖, 通讯补贴, 免费午餐, 绩效奖金, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319330", "job_id": "8f307976888ac322"}, {"title": "软件测试工程师", "company": "唯品会", "location": "福州-高新区", "salary": "12K-20K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉性能测试, 接口测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "技术津贴, 住房补贴, 培训机会, 交通补贴, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319454", "job_id": "011a1e57d720e4df"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "西安-高新区", "salary": "20K-35K", "education": "大专", "experience": "1-3年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "1-3年工作经验; 大专及以上学历; 熟悉自动化测试, 测试框架, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 住房补贴, 补充医疗保险, 交通补贴, 五险一金, 健身房, 培训机会, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319575", "job_id": "af7b54e265954195"}, {"title": "软件测试工程师", "company": "哈啰出行", "location": "长沙-经济开发区", "salary": "20K-35K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉接口测试, 性能测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "带薪年假, 年终奖, 住房补贴, 通讯补贴, 下午茶, 补充医疗保险, 健身房, 团建活动", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319699", "job_id": "117defb51eee2aa4"}, {"title": "软件测试工程师", "company": "绿盟科技", "location": "沈阳-市中心", "salary": "20K-35K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉性能测试, 测试框架, 自动化测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "通讯补贴, 交通补贴, 团建活动, 住房补贴, 补充医疗保险", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319817", "job_id": "8760f9a87b90bb37"}, {"title": "运维工程师", "company": "货拉拉", "location": "苏州-经济开发区", "salary": "28K-50K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉监控, Shell, 数据库; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "住房补贴, 股票期权, 技术津贴, 培训机会, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.319933", "job_id": "f057c83dab13eb5a"}, {"title": "运维工程师", "company": "满帮集团", "location": "石家庄-高新区", "salary": "15K-25K", "education": "本科", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 本科及以上学历; 熟悉Shell, 数据库, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "远程办公, 年终奖, 培训机会, 五险一金, 免费午餐, 带薪年假, 弹性工作", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320048", "job_id": "99ec9a67c1537bf5"}, {"title": "软件测试工程师", "company": "国美在线", "location": "石家庄-市中心", "salary": "15K-25K", "education": "本科", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 本科及以上学历; 熟悉测试框架, 自动化测试, 性能测试; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "健身房, 弹性工作, 团建活动, 股票期权, 技术津贴, 补充医疗保险, 五险一金, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320169", "job_id": "2bc5b7c8bddb6001"}, {"title": "运维工程师", "company": "启明星辰", "location": "厦门-经济开发区", "salary": "28K-50K", "education": "大专", "experience": "5-12年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "5-12年工作经验; 大专及以上学历; 熟悉Shell, 网络, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 免费班车, 交通补贴, 带薪年假, 团建活动, 培训机会", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320291", "job_id": "8ad42c1385bf7f2a"}, {"title": "运维工程师", "company": "货拉拉", "location": "深圳-南山区", "salary": "20K-35K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉Shell, Linux, 网络; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "绩效奖金, 五险一金, 补充医疗保险, 免费午餐", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320409", "job_id": "831492745e32e3b8"}, {"title": "软件测试工程师", "company": "满帮集团", "location": "太原-经济开发区", "salary": "15K-25K", "education": "大专", "experience": "5-8年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "5-8年工作经验; 大专及以上学历; 熟悉自动化测试, 接口测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "培训机会, 健身房, 技术津贴, 五险一金, 补充医疗保险, 下午茶, 免费班车", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320534", "job_id": "2f4c3bc11364fe0b"}, {"title": "软件测试工程师", "company": "国美在线", "location": "北京-东城区", "salary": "15K-25K", "education": "本科", "experience": "3-5年", "description": "负责软件质量保证，测试用例设计和执行", "requirements": "3-5年工作经验; 本科及以上学历; 熟悉接口测试, 自动化测试, 测试框架; 熟悉测试理论和方法，有自动化测试经验; 具备良好的沟通能力和团队协作精神", "benefits": "股票期权, 绩效奖金, 补充医疗保险, 住房补贴, 年终奖, 团建活动, 带薪年假", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320653", "job_id": "0801f92b232f7a13"}, {"title": "运维工程师", "company": "奇安信", "location": "天津-高新区", "salary": "20K-35K", "education": "大专", "experience": "2-5年", "description": "负责系统运维，服务器管理和故障处理", "requirements": "2-5年工作经验; 大专及以上学历; 熟悉Linux, Shell, 监控; 熟悉Linux系统，有大型系统运维经验; 具备良好的沟通能力和团队协作精神", "benefits": "五险一金, 团建活动, 技术津贴, 远程办公, 弹性工作, 补充医疗保险, 绩效奖金", "source_website": "ultimate_comprehensive", "source_url": "ultimate_comprehensive://search?keyword=计算机科学与技术", "crawl_time": "2025-06-11T09:48:21.320785", "job_id": "20e4afa022e88556"}]