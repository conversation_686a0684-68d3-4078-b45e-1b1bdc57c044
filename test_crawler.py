#!/usr/bin/env python3
"""
爬虫系统测试脚本
Crawler system test script
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.utils import config, logger
from src.data_processing import DataCleaner, JobPosition


def test_config():
    """测试配置加载"""
    print("测试配置加载...")
    
    try:
        print(f"爬虫设置: {config.crawler}")
        print(f"反反爬虫设置: {config.anti_detection}")
        print(f"数据存储设置: {config.data_storage}")
        print(f"启用的网站: {[site.name for site in config.get_enabled_websites()]}")
        print("✅ 配置加载成功")
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_data_cleaner():
    """测试数据清洗器"""
    print("\n测试数据清洗器...")
    
    try:
        cleaner = DataCleaner()
        
        # 测试数据
        test_data = {
            'title': '  Python开发工程师  ',
            'company': '  某某科技有限公司  ',
            'location': '北京-朝阳区',
            'salary': '15K-25K',
            'education': '本科',
            'experience': '3-5年',
            'description': '负责Python后端开发工作...'
        }
        
        # 清洗数据
        job = cleaner.clean_job_data(test_data, "test", "http://test.com")
        
        print(f"清洗后的职位: {job.title} - {job.company}")
        print(f"是否为计算机相关: {cleaner.is_cs_related(job)}")
        print("✅ 数据清洗器测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据清洗器测试失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("\n测试日志系统...")
    
    try:
        logger.info("这是一条测试信息")
        logger.warning("这是一条测试警告")
        logger.error("这是一条测试错误")
        print("✅ 日志系统测试成功")
        return True
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False


async def test_crawler_init():
    """测试爬虫初始化"""
    print("\n测试爬虫初始化...")

    try:
        from src.crawlers import LiepinCrawler, Job51Crawler, Job58Crawler

        # 测试各个爬虫的初始化
        crawlers = {}
        test_keyword = "Python开发"

        for name, crawler_class in [
            ('liepin', LiepinCrawler),
            ('job51', Job51Crawler),
            ('job58', Job58Crawler)
        ]:
            try:
                crawler = crawler_class(test_keyword)
                crawlers[name] = crawler
                print(f"✅ {name} 爬虫初始化成功，关键词: {test_keyword}")

                # 测试URL生成
                search_url = crawler.website_config.get_search_url(test_keyword)
                print(f"   搜索URL: {search_url}")

            except Exception as e:
                print(f"❌ {name} 爬虫初始化失败: {e}")

        print(f"成功初始化 {len(crawlers)} 个爬虫")
        return len(crawlers) > 0

    except Exception as e:
        print(f"❌ 爬虫初始化测试失败: {e}")
        return False


def test_data_storage():
    """测试数据存储"""
    print("\n测试数据存储...")
    
    try:
        from src.data_processing import DataStorage
        
        storage = DataStorage()
        
        # 创建测试数据
        test_job = JobPosition(
            title="测试职位",
            company="测试公司",
            location="测试地点",
            salary="10K-15K",
            source_website="test",
            source_url="http://test.com"
        )
        
        # 测试JSON保存
        filepath = storage.save_jobs([test_job], "json")
        if filepath and Path(filepath).exists():
            print(f"✅ JSON保存测试成功: {filepath}")
            # 清理测试文件
            Path(filepath).unlink()
        else:
            print("❌ JSON保存测试失败")
            return False
        
        print("✅ 数据存储测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据存储测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("=" * 60)
    print("工业职位需求爬虫系统 - 功能测试")
    print("=" * 60)
    
    # 创建必要目录
    Path("data/output").mkdir(parents=True, exist_ok=True)
    Path("logs").mkdir(parents=True, exist_ok=True)
    
    tests = [
        ("配置系统", test_config),
        ("日志系统", test_logger),
        ("数据清洗器", test_data_cleaner),
        ("爬虫初始化", test_crawler_init),
        ("数据存储", test_data_storage),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n使用方法:")
        print("  python run_crawler.py --help")
        print("  python run_crawler.py --dry-run")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
