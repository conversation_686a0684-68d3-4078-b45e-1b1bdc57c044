2025-06-11 08:41:48 - crawler - [31mERROR[0m - logger.py:101 - 这是一条测试错误
2025-06-11 08:52:04 - crawler - [31mERROR[0m - logger.py:101 - 爬取失败 boss: https://www.zhipin.com/web/geek/job?query=数据分析&city=101010100&page=1, 错误: Unexpected error in _crawl_web at line 1395 in _crawl_web (../../../opt/anaconda3/lib/python3.12/site-packages/crawl4ai/async_crawler_strategy.py):
Error: Wait condition failed: Timeout after 30000ms waiting for selector 'networkidle0'

Code context:
1390                   try:
1391                       await self.smart_wait(
1392                           page, config.wait_for, timeout=config.page_timeout
1393                       )
1394                   except Exception as e:
1395 →                     raise RuntimeError(f"Wait condition failed: {str(e)}")
1396   
1397               # Update image dimensions if needed
1398               if not self.browser_config.text_mode:
1399                   update_image_dimensions_js = load_js_script("update_image_dimensions")
1400                   try:
2025-06-11 09:20:24 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:31 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:38 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:44 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:49 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:29:22 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:29:27 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:29:33 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:29:39 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:29:44 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
