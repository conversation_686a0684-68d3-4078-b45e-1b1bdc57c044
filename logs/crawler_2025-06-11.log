2025-06-11 08:41:48 - crawler - [32mINFO[0m - logger.py:93 - 这是一条测试信息
2025-06-11 08:41:48 - crawler - [33mWARNING[0m - logger.py:97 - 这是一条测试警告
2025-06-11 08:41:48 - crawler - [31mERROR[0m - logger.py:101 - 这是一条测试错误
2025-06-11 08:41:48 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/liepin_schema.json
2025-06-11 08:41:48 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/job51_schema.json
2025-06-11 08:41:48 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/job58_schema.json
2025-06-11 08:42:11 - crawler - [32mIN<PERSON>O[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 试运行模式：不会保存数据
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/liepin_schema.json
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: liepin, 关键词: 数据分析
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/job51_schema.json
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: job51, 关键词: 数据分析
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/job58_schema.json
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: job58, 关键词: 数据分析
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: liepin, 最大页数: 1
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 08:42:11 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取 liepin: https://www.liepin.com/zhaopin/?key=数据分析&curPage=1
2025-06-11 08:42:27 - crawler - [32mINFO[0m - logger.py:93 - 成功爬取 liepin: https://www.liepin.com/zhaopin/?key=数据分析&curPage=1, 获取 0 条数据
2025-06-11 08:42:27 - crawler - [33mWARNING[0m - logger.py:97 - 第 1 页没有获取到数据
2025-06-11 08:42:27 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 0 条有效职位数据
2025-06-11 08:42:27 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 liepin: 0 条职位
2025-06-11 08:42:29 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: job51, 最大页数: 1
2025-06-11 08:42:29 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 08:42:29 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取 job51: https://search.51job.com/list/000000,000000,0000,00,9,99,数据分析,2,1.html
2025-06-11 08:42:37 - crawler - [32mINFO[0m - logger.py:93 - 成功爬取 job51: https://search.51job.com/list/000000,000000,0000,00,9,99,数据分析,2,1.html, 获取 0 条数据
2025-06-11 08:42:37 - crawler - [33mWARNING[0m - logger.py:97 - 第 1 页没有获取到数据
2025-06-11 08:42:37 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 0 条有效职位数据
2025-06-11 08:42:37 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 job51: 0 条职位
2025-06-11 08:42:39 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: job58, 最大页数: 1
2025-06-11 08:42:39 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 08:42:39 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取 job58: https://www.58.com/sou/?key=数据分析
2025-06-11 08:42:46 - crawler - [32mINFO[0m - logger.py:93 - 成功爬取 job58: https://www.58.com/sou/?key=数据分析, 获取 0 条数据
2025-06-11 08:42:46 - crawler - [33mWARNING[0m - logger.py:97 - 第 1 页没有获取到数据
2025-06-11 08:42:46 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 0 条有效职位数据
2025-06-11 08:42:46 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 job58: 0 条职位
2025-06-11 08:42:46 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 0 条职位，耗时 34.57 秒
2025-06-11 08:42:46 - crawler - [32mINFO[0m - logger.py:93 - 试运行完成，共获取 0 条职位数据（未保存）
2025-06-11 08:42:46 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，未获取到数据
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: boss
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 试运行模式：不会保存数据
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/boss_schema.json
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: boss, 关键词: 数据分析
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/zhilian_schema.json
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: zhilian, 关键词: 数据分析
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: boss
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: boss, 最大页数: 1
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 08:51:29 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取 boss: https://www.zhipin.com/web/geek/job?query=数据分析&city=101010100&page=1
2025-06-11 08:52:04 - crawler - [31mERROR[0m - logger.py:101 - 爬取失败 boss: https://www.zhipin.com/web/geek/job?query=数据分析&city=101010100&page=1, 错误: Unexpected error in _crawl_web at line 1395 in _crawl_web (../../../opt/anaconda3/lib/python3.12/site-packages/crawl4ai/async_crawler_strategy.py):
Error: Wait condition failed: Timeout after 30000ms waiting for selector 'networkidle0'

Code context:
1390                   try:
1391                       await self.smart_wait(
1392                           page, config.wait_for, timeout=config.page_timeout
1393                       )
1394                   except Exception as e:
1395 →                     raise RuntimeError(f"Wait condition failed: {str(e)}")
1396   
1397               # Update image dimensions if needed
1398               if not self.browser_config.text_mode:
1399                   update_image_dimensions_js = load_js_script("update_image_dimensions")
1400                   try:
2025-06-11 08:52:04 - crawler - [33mWARNING[0m - logger.py:97 - 第 1 页没有获取到数据
2025-06-11 08:52:04 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 0 条有效职位数据
2025-06-11 08:52:04 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 boss: 0 条职位
2025-06-11 08:52:04 - crawler - [32mINFO[0m - logger.py:93 - 试运行完成，共获取 0 条职位数据（未保存）
2025-06-11 08:52:04 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，未获取到数据
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: demo
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 2
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 试运行模式：不会保存数据
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/demo_schema.json
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: demo, 关键词: 数据分析
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: demo
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: demo, 最大页数: 2
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取第 1 页数据
2025-06-11 08:55:15 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取 demo: demo://jobs/search?keyword=数据分析&page=1
2025-06-11 08:55:16 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取成功，获取 13 条数据
2025-06-11 08:55:18 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取第 2 页数据
2025-06-11 08:55:18 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取 demo: demo://jobs/search?keyword=数据分析&page=2
2025-06-11 08:55:20 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取成功，获取 12 条数据
2025-06-11 08:55:20 - crawler - [32mINFO[0m - logger.py:93 - 模拟爬取完成，共获取 11 条有效职位数据
2025-06-11 08:55:20 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 demo: 11 条职位
2025-06-11 08:55:20 - crawler - [32mINFO[0m - logger.py:93 - 试运行完成，共获取 11 条职位数据（未保存）
2025-06-11 08:55:20 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 11 条职位数据
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: real
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 2
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 试运行模式：不会保存数据
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/real_schema.json
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: real, 关键词: 数据分析
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: real
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: real, 最大页数: 2
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=1
2025-06-11 08:59:49 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: github_jobs_api
2025-06-11 08:59:49 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: github_jobs_api, 错误: 'AntiDetectionManager' object has no attribute 'AntiDetectionManager'
2025-06-11 08:59:51 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 08:59:51 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: remoteok_api, 错误: 'AntiDetectionManager' object has no attribute 'AntiDetectionManager'
2025-06-11 08:59:52 - crawler - [32mINFO[0m - logger.py:93 - 使用本地真实数据: 4 条
2025-06-11 08:59:54 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 4 条数据
2025-06-11 08:59:56 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 08:59:56 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=2
2025-06-11 08:59:56 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: github_jobs_api
2025-06-11 08:59:56 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: github_jobs_api, 错误: 'AntiDetectionManager' object has no attribute 'AntiDetectionManager'
2025-06-11 08:59:58 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 08:59:58 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: remoteok_api, 错误: 'AntiDetectionManager' object has no attribute 'AntiDetectionManager'
2025-06-11 08:59:59 - crawler - [32mINFO[0m - logger.py:93 - 使用本地真实数据: 4 条
2025-06-11 09:00:01 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 4 条数据
2025-06-11 09:00:01 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 2 条有效职位数据
2025-06-11 09:00:01 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 real: 2 条职位
2025-06-11 09:00:01 - crawler - [32mINFO[0m - logger.py:93 - 试运行完成，共获取 2 条职位数据（未保存）
2025-06-11 09:00:01 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 2 条职位数据
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: csv
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: real
2025-06-11 09:00:28 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 2
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/real_schema.json
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: real, 关键词: 数据分析
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: real
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: real, 最大页数: 2
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=1
2025-06-11 09:00:34 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: github_jobs_api
2025-06-11 09:00:34 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: github_jobs_api, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:00:35 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:00:42 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:00:46 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:00:49 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 09:00:49 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=2
2025-06-11 09:00:49 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: github_jobs_api
2025-06-11 09:00:50 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: github_jobs_api, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:00:51 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:00:52 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:00:56 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:00:56 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 9 条有效职位数据
2025-06-11 09:00:56 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 real: 9 条职位
2025-06-11 09:00:56 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_090056.csv
2025-06-11 09:00:56 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_090056.json
2025-06-11 09:01:00 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_090056.xlsx
2025-06-11 09:01:00 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 9 条职位数据
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: real
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 试运行模式：不会保存数据
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/real_schema.json
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: real, 关键词: 数据分析
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/enhanced_schema.json
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: enhanced, 关键词: 数据分析
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: real
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: real, 最大页数: 1
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=1
2025-06-11 09:09:16 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:09:18 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:09:19 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:09:19 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:09:21 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:09:22 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:09:25 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:09:25 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 9 条有效职位数据
2025-06-11 09:09:25 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 real: 9 条职位
2025-06-11 09:09:25 - crawler - [32mINFO[0m - logger.py:93 - 试运行完成，共获取 9 条职位数据（未保存）
2025-06-11 09:09:25 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 9 条职位数据
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 数据分析
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: csv
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: real
2025-06-11 09:09:41 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 2
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/real_schema.json
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: real, 关键词: 数据分析
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/enhanced_schema.json
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: enhanced, 关键词: 数据分析
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: real
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: real, 最大页数: 2
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=1
2025-06-11 09:09:45 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:09:46 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:09:48 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:09:48 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:09:49 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:09:50 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:09:53 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:09:55 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 09:09:55 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&page=2
2025-06-11 09:09:55 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:09:56 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:09:58 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:09:58 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:09:59 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:10:00 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:10:02 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:10:02 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 9 条有效职位数据
2025-06-11 09:10:02 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 real: 9 条职位
2025-06-11 09:10:02 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_091002.csv
2025-06-11 09:10:02 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_091002.json
2025-06-11 09:10:03 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_091002.xlsx
2025-06-11 09:10:03 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 9 条职位数据
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: Python开发
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 指定网站: real
2025-06-11 09:11:01 - crawler - [32mINFO[0m - logger.py:93 - 最大页数: 1
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/real_schema.json
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: real, 关键词: Python开发
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/enhanced_schema.json
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: enhanced, 关键词: Python开发
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 爬取指定网站: real
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: real, 最大页数: 1
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=Python%E5%BC%80%E5%8F%91&page=1
2025-06-11 09:12:05 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:12:06 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:12:08 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:12:08 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:12:09 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:12:11 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:12:12 - crawler - [32mINFO[0m - logger.py:93 - 使用本地真实数据: 15 条
2025-06-11 09:12:14 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 24 条数据
2025-06-11 09:12:14 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 14 条有效职位数据
2025-06-11 09:12:14 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 real: 14 条职位
2025-06-11 09:12:14 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_091214.json
2025-06-11 09:12:14 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_091214.xlsx
2025-06-11 09:12:14 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 14 条职位数据
2025-06-11 09:19:16 - crawler - [32mINFO[0m - logger.py:93 - 启动工业职位需求爬虫系统
2025-06-11 09:19:16 - crawler - [32mINFO[0m - logger.py:93 - 搜索关键词: 计算机科学与技术
2025-06-11 09:19:16 - crawler - [32mINFO[0m - logger.py:93 - 配置文件: config/crawler_config.yaml
2025-06-11 09:19:16 - crawler - [32mINFO[0m - logger.py:93 - 输出格式: json
2025-06-11 09:19:16 - crawler - [32mINFO[0m - logger.py:93 - 输出目录: data/output
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/real_schema.json
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: real, 关键词: 计算机科学与技术
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 加载提取模式: config/extraction_schemas/enhanced_schema.json
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 初始化爬虫: enhanced, 关键词: 计算机科学与技术
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取所有网站
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: real, 最大页数: 5
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=1
2025-06-11 09:19:21 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:19:23 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:19:24 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:19:24 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:19:26 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:19:27 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:19:29 - crawler - [32mINFO[0m - logger.py:93 - 使用本地数据（通用）: 0 条
2025-06-11 09:19:31 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:19:34 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 09:19:34 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=2
2025-06-11 09:19:34 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:19:36 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:19:38 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:19:39 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:19:40 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:19:41 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:19:43 - crawler - [32mINFO[0m - logger.py:93 - 使用本地数据（通用）: 0 条
2025-06-11 09:19:45 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:19:48 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 09:19:48 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=3
2025-06-11 09:19:48 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:19:50 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:19:51 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:19:51 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:19:53 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:19:54 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:19:55 - crawler - [32mINFO[0m - logger.py:93 - 使用本地数据（通用）: 0 条
2025-06-11 09:19:57 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:19:59 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 09:19:59 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=4
2025-06-11 09:19:59 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:20:01 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:20:02 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:20:02 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:20:04 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:20:05 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:20:07 - crawler - [32mINFO[0m - logger.py:93 - 使用本地数据（通用）: 0 条
2025-06-11 09:20:08 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:20:12 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 09:20:12 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取: real://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=5
2025-06-11 09:20:12 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: remoteok_api
2025-06-11 09:20:13 - crawler - [32mINFO[0m - logger.py:93 - 从 remoteok_api 获取到 9 条数据
2025-06-11 09:20:15 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: jobs_github_io
2025-06-11 09:20:15 - crawler - [33mWARNING[0m - logger.py:97 - API请求异常: jobs_github_io, 错误: Cannot connect to host jobs.github.com:443 ssl:default [None]
2025-06-11 09:20:16 - crawler - [32mINFO[0m - logger.py:93 - 尝试从API获取数据: usajobs_api
2025-06-11 09:20:17 - crawler - [33mWARNING[0m - logger.py:97 - API请求失败: usajobs_api, 状态码: 401
2025-06-11 09:20:18 - crawler - [32mINFO[0m - logger.py:93 - 使用本地数据（通用）: 0 条
2025-06-11 09:20:20 - crawler - [32mINFO[0m - logger.py:93 - 真实数据爬取完成，共获取 9 条数据
2025-06-11 09:20:20 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 9 条有效职位数据
2025-06-11 09:20:20 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 real: 9 条职位
2025-06-11 09:20:22 - crawler - [32mINFO[0m - logger.py:93 - 开始爬取网站: enhanced, 最大页数: 5
2025-06-11 09:20:22 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 1 页数据
2025-06-11 09:20:22 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫开始: enhanced://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=1
2025-06-11 09:20:22 - crawler - [32mINFO[0m - logger.py:93 - Boss直聘获取到 0 条职位
2025-06-11 09:20:22 - crawler - [32mINFO[0m - logger.py:93 - 智联招聘获取到 0 条职位
2025-06-11 09:20:24 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:28 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫完成，共获取 0 条数据
2025-06-11 09:20:28 - crawler - [33mWARNING[0m - logger.py:97 - 第 1 页没有获取到数据
2025-06-11 09:20:28 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 2 页数据
2025-06-11 09:20:28 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫开始: enhanced://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=2
2025-06-11 09:20:28 - crawler - [32mINFO[0m - logger.py:93 - 智联招聘获取到 0 条职位
2025-06-11 09:20:28 - crawler - [32mINFO[0m - logger.py:93 - Boss直聘获取到 0 条职位
2025-06-11 09:20:31 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:35 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫完成，共获取 0 条数据
2025-06-11 09:20:35 - crawler - [33mWARNING[0m - logger.py:97 - 第 2 页没有获取到数据
2025-06-11 09:20:35 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 3 页数据
2025-06-11 09:20:35 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫开始: enhanced://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=3
2025-06-11 09:20:35 - crawler - [32mINFO[0m - logger.py:93 - Boss直聘获取到 0 条职位
2025-06-11 09:20:35 - crawler - [32mINFO[0m - logger.py:93 - 智联招聘获取到 0 条职位
2025-06-11 09:20:38 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:41 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫完成，共获取 0 条数据
2025-06-11 09:20:41 - crawler - [33mWARNING[0m - logger.py:97 - 第 3 页没有获取到数据
2025-06-11 09:20:41 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 4 页数据
2025-06-11 09:20:41 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫开始: enhanced://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=4
2025-06-11 09:20:41 - crawler - [32mINFO[0m - logger.py:93 - Boss直聘获取到 0 条职位
2025-06-11 09:20:41 - crawler - [32mINFO[0m - logger.py:93 - 智联招聘获取到 0 条职位
2025-06-11 09:20:44 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:47 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫完成，共获取 0 条数据
2025-06-11 09:20:47 - crawler - [33mWARNING[0m - logger.py:97 - 第 4 页没有获取到数据
2025-06-11 09:20:47 - crawler - [32mINFO[0m - logger.py:93 - 爬取第 5 页数据
2025-06-11 09:20:47 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫开始: enhanced://jobs/search?keyword=%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%A7%91%E5%AD%A6%E4%B8%8E%E6%8A%80%E6%9C%AF&page=5
2025-06-11 09:20:47 - crawler - [32mINFO[0m - logger.py:93 - Boss直聘获取到 0 条职位
2025-06-11 09:20:47 - crawler - [32mINFO[0m - logger.py:93 - 智联招聘获取到 0 条职位
2025-06-11 09:20:49 - crawler - [31mERROR[0m - logger.py:101 - 拉勾网爬取失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/html;charset=utf-8', url='https://passport.lagou.com/login/login.html?msg=needlogin&clientIp=**************'
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - 增强爬虫完成，共获取 0 条数据
2025-06-11 09:20:53 - crawler - [33mWARNING[0m - logger.py:97 - 第 5 页没有获取到数据
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取，共获取 0 条有效职位数据
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - 完成爬取 enhanced: 0 条职位
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - 完成所有网站爬取: 总计 9 条职位，耗时 91.60 秒
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - 职位数据已保存: data/output/jobs_20250611_092053.json
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - Excel文件已导出: data/output/jobs_export_20250611_092053.xlsx
2025-06-11 09:20:53 - crawler - [32mINFO[0m - logger.py:93 - 爬取完成，共获取 9 条职位数据
