"""
智联招聘爬虫
Zhilian.com crawler
"""

import re
from typing import Dict, Any, List
from urllib.parse import urljoin, quote

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class ZhilianCrawler(BaseCrawler):
    """智联招聘爬虫"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("zhilian", keyword)
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取智联招聘默认提取模式"""
        return {
            "name": "Zhilian Jobs",
            "baseSelector": ".newlist_list_content table, .zwmc, .joblist-item",
            "fields": [
                {
                    "name": "title",
                    "selector": ".zwmc a, .job-title a, .position-title a",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".gsmc a, .company-name a, .company a",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".gzdd, .job-location, .workarea",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".zwyx, .job-salary, .salary",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".gzjy, .job-experience, .experience",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".xueli, .job-education, .education",
                    "type": "text"
                },
                {
                    "name": "job_url",
                    "selector": ".zwmc a, .job-title a, .position-title a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "company_url",
                    "selector": ".gsmc a, .company-name a, .company a",
                    "type": "attribute", 
                    "attribute": "href"
                },
                {
                    "name": "publish_time",
                    "selector": ".gxsj, .job-time, .time",
                    "type": "text"
                }
            ]
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建智联招聘搜索URL"""
        # 智联招聘的分页参数通常是p
        if "?" in base_url:
            separator = "&"
        else:
            separator = "?"
        
        return f"{base_url}{separator}p={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取智联招聘职位详情页"""
        if not job_url:
            return {}
        
        # 确保URL是完整的
        if job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        
        crawler_logger.debug(f"爬取职位详情: {job_url}")
        
        # 职位详情页的提取模式
        detail_schema = {
            "name": "Zhilian Job Detail",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-title, .position-title h1",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name, .company-info h3",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".salary-text, .job-salary",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-location, .work-location",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".job-require .experience",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".job-require .education",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": ".job-description, .job-detail-content",
                    "type": "text"
                },
                {
                    "name": "requirements",
                    "selector": ".job-require-text, .job-qualifications",
                    "type": "text"
                },
                {
                    "name": "benefits",
                    "selector": ".job-benefit, .welfare-list",
                    "type": "text"
                }
            ]
        }
        
        # 使用详情页模式爬取
        detail_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(detail_schema),
            js_code=anti_detection.get_js_stealth_code(),
            verbose=True
        )
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                result = await crawler.arun(url=job_url, config=detail_config)
                
                if result.success:
                    detail_data = self._parse_extracted_data(result.extracted_content)
                    if detail_data:
                        return detail_data[0] if isinstance(detail_data, list) else detail_data
                
        except Exception as e:
            crawler_logger.error(f"爬取职位详情失败: {e}")
        
        return {}
    
    def _clean_zhilian_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗智联招聘特有的数据格式"""
        cleaned = {}
        
        # 职位标题
        title = raw_data.get('title', '').strip()
        cleaned['title'] = re.sub(r'[\r\n\t]+', ' ', title)
        
        # 公司名称
        company = raw_data.get('company', '').strip()
        cleaned['company'] = re.sub(r'[\r\n\t]+', ' ', company)
        
        # 工作地点
        location = raw_data.get('location', '').strip()
        location = re.sub(r'[\r\n\t]+', ' ', location)
        cleaned['location'] = location
        
        # 薪资处理
        salary = raw_data.get('salary', '').strip()
        # 移除多余的空白字符和特殊符号
        salary = re.sub(r'[\r\n\t]+', ' ', salary)
        cleaned['salary'] = salary
        
        # 工作经验
        experience = raw_data.get('experience', '').strip()
        cleaned['experience'] = re.sub(r'[\r\n\t]+', ' ', experience)
        
        # 学历要求
        education = raw_data.get('education', '').strip()
        cleaned['education'] = re.sub(r'[\r\n\t]+', ' ', education)
        
        # 职位描述
        description = raw_data.get('description', '').strip()
        description = re.sub(r'[\r\n\t]+', ' ', description)
        cleaned['description'] = description
        
        # 职位要求
        requirements = raw_data.get('requirements', '').strip()
        requirements = re.sub(r'[\r\n\t]+', ' ', requirements)
        cleaned['requirements'] = requirements
        
        # 福利待遇
        benefits = raw_data.get('benefits', '').strip()
        benefits = re.sub(r'[\r\n\t]+', ' ', benefits)
        cleaned['benefits'] = benefits
        
        # 发布时间
        publish_time = raw_data.get('publish_time', '').strip()
        cleaned['publish_time'] = re.sub(r'[\r\n\t]+', ' ', publish_time)
        
        # URL处理
        job_url = raw_data.get('job_url', '')
        if job_url and job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        cleaned['job_url'] = job_url
        
        return cleaned
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """重写搜索页面爬取，添加智联招聘特有的处理"""
        jobs_data = await super().crawl_search_page(page)
        
        # 对智联招聘数据进行特殊清洗
        cleaned_jobs = []
        for job_data in jobs_data:
            cleaned_job = self._clean_zhilian_data(job_data)
            if cleaned_job.get('title') and cleaned_job.get('company'):
                cleaned_jobs.append(cleaned_job)
        
        return cleaned_jobs
