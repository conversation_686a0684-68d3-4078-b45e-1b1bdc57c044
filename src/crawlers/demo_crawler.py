"""
演示爬虫 - 生成模拟数据用于测试系统功能
Demo crawler - Generate mock data for testing system functionality
"""

import random
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta

from .base_crawler import BaseCrawler
from ..utils import crawler_logger
from ..data_processing import JobPosition


class DemoCrawler(BaseCrawler):
    """演示爬虫 - 生成模拟职位数据"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        # 使用特殊的网站名称
        super().__init__("demo", keyword)
        
        # 模拟数据模板
        self.job_titles = [
            "Python开发工程师", "Java后端开发", "前端开发工程师", "数据分析师", 
            "算法工程师", "机器学习工程师", "全栈开发工程师", "DevOps工程师",
            "大数据开发工程师", "人工智能工程师", "区块链开发工程师", "云计算工程师",
            "网络安全工程师", "移动端开发工程师", "测试开发工程师", "产品经理"
        ]
        
        self.companies = [
            "阿里巴巴", "腾讯科技", "百度", "字节跳动", "美团", "滴滴出行",
            "京东", "网易", "新浪", "搜狐", "360", "小米科技", "华为技术",
            "中兴通讯", "联想集团", "海康威视", "科大讯飞", "商汤科技",
            "旷视科技", "依图科技", "云从科技", "第四范式", "明略科技"
        ]
        
        self.locations = [
            "北京-朝阳区", "北京-海淀区", "上海-浦东新区", "上海-徐汇区",
            "深圳-南山区", "深圳-福田区", "广州-天河区", "杭州-西湖区",
            "成都-高新区", "武汉-光谷", "南京-江宁区", "苏州-工业园区"
        ]
        
        self.salaries = [
            "8K-15K", "10K-18K", "15K-25K", "20K-35K", "25K-40K", "30K-50K",
            "12K-20K", "18K-30K", "22K-38K", "28K-45K", "35K-60K", "40K-70K"
        ]
        
        self.experiences = [
            "1-3年", "3-5年", "5-10年", "不限", "应届毕业生", "10年以上"
        ]
        
        self.educations = [
            "本科", "硕士", "博士", "大专", "不限"
        ]
        
        self.benefits = [
            "五险一金", "弹性工作", "股票期权", "年终奖", "带薪年假", "免费班车",
            "健身房", "下午茶", "团建活动", "培训机会", "晋升空间", "技术氛围"
        ]
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "Demo Jobs",
            "baseSelector": ".demo-job-item",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"demo://jobs/search?keyword={self.keyword}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.5, 1.5))
        
        return {
            "description": f"负责{self.keyword}相关的技术开发工作，参与系统架构设计和优化。",
            "requirements": f"熟悉{self.keyword}相关技术栈，具备良好的编程能力和团队协作精神。",
            "benefits": random.sample(self.benefits, random.randint(3, 6))
        }
    
    def _generate_mock_job(self) -> Dict[str, Any]:
        """生成模拟职位数据"""
        # 根据关键词调整职位标题
        if "数据" in self.keyword:
            relevant_titles = [t for t in self.job_titles if "数据" in t or "算法" in t or "机器学习" in t]
        elif "Python" in self.keyword:
            relevant_titles = [t for t in self.job_titles if "Python" in t or "后端" in t or "全栈" in t]
        elif "前端" in self.keyword:
            relevant_titles = [t for t in self.job_titles if "前端" in t or "全栈" in t]
        else:
            relevant_titles = self.job_titles
        
        if not relevant_titles:
            relevant_titles = self.job_titles
        
        # 生成职位数据
        job_data = {
            "title": random.choice(relevant_titles),
            "company": random.choice(self.companies),
            "location": random.choice(self.locations),
            "salary": random.choice(self.salaries),
            "experience": random.choice(self.experiences),
            "education": random.choice(self.educations),
            "job_url": f"demo://job/{random.randint(100000, 999999)}",
            "company_url": f"demo://company/{random.randint(1000, 9999)}",
            "publish_time": self._generate_publish_time(),
            "description": f"负责{self.keyword}相关的技术开发工作。",
            "requirements": f"熟悉{self.keyword}相关技术，有相关项目经验。",
            "benefits": ", ".join(random.sample(self.benefits, random.randint(3, 5)))
        }
        
        return job_data
    
    def _generate_publish_time(self) -> str:
        """生成发布时间"""
        days_ago = random.randint(0, 30)
        publish_date = datetime.now() - timedelta(days=days_ago)
        
        if days_ago == 0:
            return "今天"
        elif days_ago == 1:
            return "昨天"
        elif days_ago <= 7:
            return f"{days_ago}天前"
        else:
            return publish_date.strftime("%m-%d")
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """模拟爬取搜索页面"""
        search_url = self._build_search_url("demo://", page)
        crawler_logger.info(f"模拟爬取 demo: {search_url}")
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(1.0, 3.0))
        
        # 生成模拟数据
        job_count = random.randint(8, 15)  # 每页8-15个职位
        jobs_data = []
        
        for _ in range(job_count):
            job_data = self._generate_mock_job()
            jobs_data.append(job_data)
        
        crawler_logger.info(f"模拟爬取成功，获取 {len(jobs_data)} 条数据")
        return jobs_data
    
    async def crawl_multiple_pages(self, max_pages: int = 5) -> List[JobPosition]:
        """重写多页爬取方法"""
        all_jobs = []
        
        for page in range(1, max_pages + 1):
            crawler_logger.info(f"模拟爬取第 {page} 页数据")
            
            try:
                # 爬取搜索页面
                jobs_data = await self.crawl_search_page(page)
                
                if not jobs_data:
                    crawler_logger.warning(f"第 {page} 页没有获取到数据")
                    continue
                
                # 清洗数据
                cleaned_jobs = self.data_cleaner.clean_job_list(
                    jobs_data,
                    "demo",
                    f"demo://search?page={page}"
                )
                
                all_jobs.extend(cleaned_jobs)
                
                # 添加页面间延迟
                if page < max_pages:
                    await asyncio.sleep(random.uniform(1.0, 2.0))
                
            except Exception as e:
                crawler_logger.error(f"模拟爬取第 {page} 页失败: {e}")
                continue
        
        crawler_logger.info(f"模拟爬取完成，共获取 {len(all_jobs)} 条有效职位数据")
        return all_jobs
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'demo',
            'keyword': self.keyword,
            'base_url': 'demo://',
            'search_url': f'demo://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'mock_data_generator',
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
