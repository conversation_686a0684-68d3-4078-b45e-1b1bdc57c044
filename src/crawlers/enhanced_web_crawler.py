"""
增强网页爬虫 - 使用多种策略绕过反爬虫机制
Enhanced web crawler - Use multiple strategies to bypass anti-bot mechanisms
"""

import asyncio
import aiohttp
import random
import json
from typing import Dict, Any, List
from urllib.parse import quote, urljoin
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import re

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class EnhancedWebCrawler(BaseCrawler):
    """增强网页爬虫 - 多策略反反爬虫"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("enhanced", keyword)
        
        # 目标网站配置
        self.target_sites = [
            {
                "name": "lagou",
                "search_url": "https://www.lagou.com/jobs/positionAjax.json",
                "method": "ajax",
                "headers": {
                    "Referer": "https://www.lagou.com/jobs/list_{}".format(quote(keyword)),
                    "X-Requested-With": "XMLHttpRequest"
                },
                "params": {
                    "needAddtionalResult": "false",
                    "isSchoolJob": "0"
                }
            },
            {
                "name": "zhilian",
                "search_url": "https://fe-api.zhaopin.com/c/i/sou",
                "method": "api",
                "headers": {
                    "Referer": "https://sou.zhaopin.com/",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                }
            },
            {
                "name": "boss_mobile",
                "search_url": "https://www.zhipin.com/wapi/zpgeek/search/joblist.json",
                "method": "mobile_api",
                "headers": {
                    "Referer": "https://www.zhipin.com/",
                    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"
                }
            }
        ]
        
        # 代理池配置
        self.proxy_list = [
            # 可以添加免费代理或付费代理
            # "http://proxy1:port",
            # "http://proxy2:port",
        ]
        
        # 会话管理
        self.sessions = {}
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "Enhanced Jobs",
            "baseSelector": ".enhanced-job-item",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"enhanced://jobs/search?keyword={quote(self.keyword)}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        await asyncio.sleep(random.uniform(0.3, 0.8))
        return {}
    
    async def _create_session(self, site_config: Dict[str, Any]) -> aiohttp.ClientSession:
        """创建会话"""
        # 获取反检测headers
        headers = anti_detection.get_headers()
        headers.update(site_config.get("headers", {}))
        
        # 配置连接器
        connector = aiohttp.TCPConnector(
            limit=10,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        # 配置超时
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        # 创建会话
        session = aiohttp.ClientSession(
            headers=headers,
            connector=connector,
            timeout=timeout,
            cookie_jar=aiohttp.CookieJar()
        )
        
        return session
    
    async def _fetch_lagou_jobs(self, keyword: str, page: int = 1) -> List[Dict[str, Any]]:
        """爬取拉勾网职位"""
        jobs = []
        
        try:
            site_config = next(s for s in self.target_sites if s["name"] == "lagou")
            
            async with await self._create_session(site_config) as session:
                # 首先访问搜索页面获取cookies
                search_page_url = f"https://www.lagou.com/jobs/list_{quote(keyword)}"
                await session.get(search_page_url)
                
                # 等待一下
                await asyncio.sleep(random.uniform(1, 3))
                
                # 构建AJAX请求参数
                data = {
                    "first": "true" if page == 1 else "false",
                    "pn": str(page),
                    "kd": keyword
                }
                data.update(site_config["params"])
                
                # 发送AJAX请求
                async with session.post(site_config["search_url"], data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get("success"):
                            job_list = result.get("content", {}).get("positionResult", {}).get("result", [])
                            
                            for job_data in job_list:
                                job = {
                                    "title": job_data.get("positionName", ""),
                                    "company": job_data.get("companyFullName", ""),
                                    "location": f"{job_data.get('city', '')}-{job_data.get('district', '')}",
                                    "salary": job_data.get("salary", ""),
                                    "experience": job_data.get("workYear", ""),
                                    "education": job_data.get("education", ""),
                                    "description": job_data.get("positionAdvantage", ""),
                                    "job_url": f"https://www.lagou.com/jobs/{job_data.get('positionId', '')}.html",
                                    "company_url": f"https://www.lagou.com/gongsi/{job_data.get('companyId', '')}.html",
                                    "publish_time": job_data.get("createTime", "")
                                }
                                jobs.append(job)
                        
                        crawler_logger.info(f"拉勾网获取到 {len(jobs)} 条职位")
                    
        except Exception as e:
            crawler_logger.error(f"拉勾网爬取失败: {e}")
        
        return jobs
    
    async def _fetch_zhilian_jobs(self, keyword: str, page: int = 1) -> List[Dict[str, Any]]:
        """爬取智联招聘职位"""
        jobs = []
        
        try:
            site_config = next(s for s in self.target_sites if s["name"] == "zhilian")
            
            async with await self._create_session(site_config) as session:
                # 构建请求参数
                params = {
                    "jl": "530",  # 地区代码
                    "kw": keyword,
                    "p": page,
                    "pageSize": 60
                }
                
                async with session.get(site_config["search_url"], params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get("code") == 200:
                            job_list = result.get("data", {}).get("results", [])
                            
                            for job_data in job_list:
                                job = {
                                    "title": job_data.get("jobName", ""),
                                    "company": job_data.get("company", {}).get("name", ""),
                                    "location": job_data.get("cityDistrict", ""),
                                    "salary": job_data.get("salary", ""),
                                    "experience": job_data.get("workingExp", ""),
                                    "education": job_data.get("eduLevel", ""),
                                    "description": job_data.get("jobType", ""),
                                    "job_url": job_data.get("positionURL", ""),
                                    "publish_time": job_data.get("updateDate", "")
                                }
                                jobs.append(job)
                        
                        crawler_logger.info(f"智联招聘获取到 {len(jobs)} 条职位")
                    
        except Exception as e:
            crawler_logger.error(f"智联招聘爬取失败: {e}")
        
        return jobs
    
    async def _fetch_boss_mobile_jobs(self, keyword: str, page: int = 1) -> List[Dict[str, Any]]:
        """通过移动端API爬取Boss直聘职位"""
        jobs = []
        
        try:
            site_config = next(s for s in self.target_sites if s["name"] == "boss_mobile")
            
            async with await self._create_session(site_config) as session:
                # 构建请求参数
                params = {
                    "scene": 1,
                    "query": keyword,
                    "city": 101010100,  # 北京
                    "experience": "",
                    "payType": "",
                    "partTime": "",
                    "degree": "",
                    "industry": "",
                    "scale": "",
                    "stage": "",
                    "position": "",
                    "jobType": "",
                    "salary": "",
                    "multiBusinessDistrict": "",
                    "multiSubway": "",
                    "page": page,
                    "pageSize": 30
                }
                
                async with session.get(site_config["search_url"], params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get("code") == 0:
                            job_list = result.get("zpData", {}).get("jobList", [])
                            
                            for job_data in job_list:
                                job = {
                                    "title": job_data.get("jobName", ""),
                                    "company": job_data.get("brandName", ""),
                                    "location": f"{job_data.get('cityName', '')}-{job_data.get('areaDistrict', '')}",
                                    "salary": job_data.get("salaryDesc", ""),
                                    "experience": job_data.get("jobExperience", ""),
                                    "education": job_data.get("jobDegree", ""),
                                    "description": job_data.get("jobLabels", []),
                                    "job_url": f"https://www.zhipin.com/job_detail/{job_data.get('encryptJobId', '')}.html",
                                    "publish_time": job_data.get("lastModifyTime", "")
                                }
                                jobs.append(job)
                        
                        crawler_logger.info(f"Boss直聘获取到 {len(jobs)} 条职位")
                    
        except Exception as e:
            crawler_logger.error(f"Boss直聘爬取失败: {e}")
        
        return jobs
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 多网站并行"""
        search_url = self._build_search_url("enhanced://", page)
        crawler_logger.info(f"增强爬虫开始: {search_url}")
        
        all_jobs = []
        
        # 并行爬取多个网站
        tasks = [
            self._fetch_lagou_jobs(self.keyword, page),
            self._fetch_zhilian_jobs(self.keyword, page),
            self._fetch_boss_mobile_jobs(self.keyword, page)
        ]
        
        # 执行并行任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                crawler_logger.error(f"网站 {i} 爬取异常: {result}")
            elif isinstance(result, list):
                all_jobs.extend(result)
        
        # 添加随机延迟
        await asyncio.sleep(random.uniform(2.0, 4.0))
        
        crawler_logger.info(f"增强爬虫完成，共获取 {len(all_jobs)} 条数据")
        return all_jobs
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'enhanced_multi_site',
            'keyword': self.keyword,
            'base_url': 'enhanced://',
            'search_url': f'enhanced://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'enhanced_multi_site_crawler',
            'target_sites': len(self.target_sites),
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
