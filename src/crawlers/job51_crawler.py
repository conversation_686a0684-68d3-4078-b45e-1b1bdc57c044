"""
前程无忧爬虫
51job.com crawler
"""

import re
from typing import Dict, Any, List
from urllib.parse import urljoin

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class Job51Crawler(BaseCrawler):
    """前程无忧爬虫"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("job51", keyword)
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取前程无忧默认提取模式"""
        return {
            "name": "51Job Jobs",
            "baseSelector": ".j_joblist .e, .joblist-item, .job-item",
            "fields": [
                {
                    "name": "title",
                    "selector": ".t1 a, .job-title a, .jobname a",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".t2 a, .company-name a, .cname a",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".t3, .job-area, .area",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".t4, .job-salary, .sal",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".t5, .job-require, .require",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".t5, .job-require, .require",
                    "type": "text"
                },
                {
                    "name": "job_url",
                    "selector": ".t1 a, .job-title a, .jobname a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "company_url",
                    "selector": ".t2 a, .company-name a, .cname a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "publish_time",
                    "selector": ".t6, .job-time, .time",
                    "type": "text"
                }
            ]
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建前程无忧搜索URL"""
        # 前程无忧的分页通常是修改URL中的页码参数
        # 例如: https://search.51job.com/list/000000,000000,0000,00,9,99,计算机科学与技术,2,1.html
        # 最后的数字是页码
        
        if page == 1:
            return base_url
        
        # 尝试替换URL中的页码
        if base_url.endswith('.html'):
            # 分析URL结构，替换最后的页码
            parts = base_url.rsplit(',', 1)
            if len(parts) == 2:
                base_part = parts[0]
                return f"{base_part},{page}.html"
        
        # 如果无法解析，尝试添加页码参数
        separator = "&" if "?" in base_url else "?"
        return f"{base_url}{separator}curr_page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取前程无忧职位详情页"""
        if not job_url:
            return {}
        
        # 确保URL是完整的
        if job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        
        crawler_logger.debug(f"爬取职位详情: {job_url}")
        
        # 职位详情页的提取模式
        detail_schema = {
            "name": "51Job Job Detail",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": ".cn h1, .job-title h1, .job-name",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".cn .cname a, .company-name",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".cn .sal, .job-salary",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".cn .msg, .job-location",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".cn .msg, .job-require .experience",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".cn .msg, .job-require .education",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": ".bmsg .job_msg, .job-description, .job-detail",
                    "type": "text"
                },
                {
                    "name": "requirements",
                    "selector": ".job-require, .job-qualifications",
                    "type": "text"
                },
                {
                    "name": "benefits",
                    "selector": ".jtag .t1, .job-benefits",
                    "type": "text"
                }
            ]
        }
        
        # 使用详情页模式爬取
        detail_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(detail_schema),
            js_code=anti_detection.get_js_stealth_code(),
            verbose=True
        )
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                result = await crawler.arun(url=job_url, config=detail_config)
                
                if result.success:
                    detail_data = self._parse_extracted_data(result.extracted_content)
                    if detail_data:
                        return detail_data[0] if isinstance(detail_data, list) else detail_data
                
        except Exception as e:
            crawler_logger.error(f"爬取职位详情失败: {e}")
        
        return {}
    
    def _clean_51job_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗前程无忧特有的数据格式"""
        cleaned = {}
        
        # 职位标题
        title = raw_data.get('title', '').strip()
        # 移除前程无忧特有的标记
        title = re.sub(r'[\r\n\t]+', ' ', title)
        title = re.sub(r'\s*\(.*?\)\s*$', '', title)  # 移除括号内容
        cleaned['title'] = title
        
        # 公司名称
        company = raw_data.get('company', '').strip()
        cleaned['company'] = re.sub(r'[\r\n\t]+', ' ', company)
        
        # 工作地点
        location = raw_data.get('location', '').strip()
        # 前程无忧地点格式处理
        location = re.sub(r'[\r\n\t]+', ' ', location)
        location = re.sub(r'工作地点[：:]?', '', location)
        cleaned['location'] = location
        
        # 薪资处理
        salary = raw_data.get('salary', '').strip()
        # 前程无忧薪资格式特殊处理
        salary = re.sub(r'[\r\n\t]+', ' ', salary)
        salary = re.sub(r'薪资[：:]?', '', salary)
        # 保留数字、K、万、元、-、/等
        salary = re.sub(r'[^\d\-/KkWw万元千·\s]', '', salary)
        cleaned['salary'] = salary
        
        # 工作经验和学历要求通常在同一个字段中
        require_text = raw_data.get('experience', '') + ' ' + raw_data.get('education', '')
        require_text = require_text.strip()
        
        # 分离经验和学历
        experience = ''
        education = ''
        
        if require_text:
            # 查找经验相关关键词
            exp_patterns = [
                r'(\d+[-~]\d+年)', r'(\d+年以上)', r'(\d+年经验)',
                r'(无经验)', r'(应届)', r'(实习)', r'(不限经验)'
            ]
            for pattern in exp_patterns:
                match = re.search(pattern, require_text)
                if match:
                    experience = match.group(1)
                    break
            
            # 查找学历相关关键词
            edu_patterns = [
                r'(博士)', r'(硕士)', r'(本科)', r'(大专)', r'(中专)',
                r'(高中)', r'(初中)', r'(不限学历)'
            ]
            for pattern in edu_patterns:
                match = re.search(pattern, require_text)
                if match:
                    education = match.group(1)
                    break
        
        cleaned['experience'] = experience
        cleaned['education'] = education
        
        # 职位描述
        description = raw_data.get('description', '').strip()
        description = re.sub(r'[\r\n\t]+', ' ', description)
        cleaned['description'] = description
        
        # 职位要求
        requirements = raw_data.get('requirements', '').strip()
        requirements = re.sub(r'[\r\n\t]+', ' ', requirements)
        cleaned['requirements'] = requirements
        
        # 福利待遇
        benefits = raw_data.get('benefits', '').strip()
        benefits = re.sub(r'[\r\n\t]+', ' ', benefits)
        cleaned['benefits'] = benefits
        
        # URL处理
        job_url = raw_data.get('job_url', '')
        if job_url and job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        cleaned['job_url'] = job_url
        
        return cleaned
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """重写搜索页面爬取，添加前程无忧特有的处理"""
        jobs_data = await super().crawl_search_page(page)
        
        # 对前程无忧数据进行特殊清洗
        cleaned_jobs = []
        for job_data in jobs_data:
            cleaned_job = self._clean_51job_data(job_data)
            if cleaned_job.get('title') and cleaned_job.get('company'):
                cleaned_jobs.append(cleaned_job)
        
        return cleaned_jobs
    
    def _extract_page_info(self, html: str) -> Dict[str, Any]:
        """提取页面信息（总页数、当前页等）"""
        page_info = {
            'current_page': 1,
            'total_pages': 1,
            'total_jobs': 0
        }
        
        try:
            import re
            
            # 查找总页数 - 前程无忧的分页信息
            total_pages_match = re.search(r'共(\d+)页', html)
            if total_pages_match:
                page_info['total_pages'] = int(total_pages_match.group(1))
            
            # 查找总职位数
            total_jobs_patterns = [
                r'共(\d+)个职位',
                r'找到(\d+)个相关职位',
                r'共找到(\d+)条'
            ]
            
            for pattern in total_jobs_patterns:
                match = re.search(pattern, html)
                if match:
                    page_info['total_jobs'] = int(match.group(1))
                    break
                    
        except Exception as e:
            crawler_logger.debug(f"提取页面信息失败: {e}")
        
        return page_info
    
    def _handle_anti_crawler(self) -> Dict[str, Any]:
        """处理前程无忧的反爬虫机制"""
        # 前程无忧可能需要特殊的请求头或cookies
        headers = {
            'Referer': 'https://www.51job.com/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        return headers
