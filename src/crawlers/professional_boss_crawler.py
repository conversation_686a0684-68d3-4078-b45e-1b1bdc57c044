"""
专业Boss直聘爬虫 - 获取全部真实招聘信息
Professional Boss Crawler - Get all real recruitment information
"""

import asyncio
import aiohttp
import json
import random
import time
from typing import Dict, Any, List
from urllib.parse import quote, urljoin
from datetime import datetime
import re

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class ProfessionalBossCrawler(BaseCrawler):
    """专业Boss直聘爬虫 - 无遗漏数据获取"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("professional_boss", keyword)
        
        # Boss直聘API端点
        self.api_endpoints = {
            "search": "https://www.zhipin.com/wapi/zpgeek/search/joblist.json",
            "detail": "https://www.zhipin.com/job_detail/",
            "company": "https://www.zhipin.com/gongsi/"
        }
        
        # 城市代码映射
        self.city_codes = {
            "全国": "*********",
            "北京": "*********", "上海": "*********", "广州": "*********", "深圳": "*********",
            "杭州": "*********", "成都": "*********", "武汉": "*********", "西安": "*********",
            "南京": "*********", "苏州": "*********", "天津": "*********", "重庆": "*********",
            "青岛": "*********", "大连": "*********", "厦门": "*********", "长沙": "*********",
            "郑州": "*********", "济南": "*********", "沈阳": "*********", "合肥": "*********"
        }
        
        # 经验代码
        self.experience_codes = {
            "不限": "", "应届生": "108", "1年以内": "101", "1-3年": "102",
            "3-5年": "103", "5-10年": "104", "10年以上": "105"
        }
        
        # 学历代码
        self.degree_codes = {
            "不限": "", "初中及以下": "209", "中专/中技": "208", "高中": "207",
            "大专": "206", "本科": "202", "硕士": "203", "博士": "204"
        }
        
        # 请求头配置
        self.headers_pool = [
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Referer": "https://www.zhipin.com/",
                "Origin": "https://www.zhipin.com",
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin"
            }
        ]
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "Professional Boss Jobs",
            "baseSelector": ".professional-boss-job",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"professional_boss://search?keyword={quote(self.keyword)}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        await asyncio.sleep(random.uniform(0.5, 1.5))
        return {}
    
    def _build_search_params(self, keyword: str, page: int, city_code: str = "*********") -> Dict[str, Any]:
        """构建搜索参数"""
        return {
            "scene": "1",
            "query": keyword,
            "city": city_code,
            "experience": "",
            "payType": "",
            "partTime": "",
            "degree": "",
            "industry": "",
            "scale": "",
            "stage": "",
            "position": "",
            "jobType": "",
            "salary": "",
            "multiBusinessDistrict": "",
            "multiSubway": "",
            "page": str(page),
            "pageSize": "30"
        }
    
    async def _fetch_boss_jobs(self, keyword: str, page: int = 1, city_code: str = "*********") -> List[Dict[str, Any]]:
        """获取Boss直聘职位数据"""
        jobs = []
        
        try:
            headers = random.choice(self.headers_pool).copy()
            headers.update(anti_detection.get_headers())
            
            # 构建请求参数
            params = self._build_search_params(keyword, page, city_code)
            
            async with aiohttp.ClientSession(headers=headers) as session:
                # 首先访问主页获取cookies
                await session.get("https://www.zhipin.com/")
                await asyncio.sleep(random.uniform(1, 2))
                
                # 发送搜索请求
                async with session.get(self.api_endpoints["search"], params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get("code") == 0:
                            job_list = result.get("zpData", {}).get("jobList", [])
                            
                            for job_data in job_list:
                                job = self._parse_boss_job(job_data)
                                if job:
                                    jobs.append(job)
                            
                            crawler_logger.info(f"Boss直聘第{page}页获取到 {len(jobs)} 条职位")
                        else:
                            crawler_logger.warning(f"Boss直聘API返回错误: {result.get('message', '未知错误')}")
                    else:
                        crawler_logger.error(f"Boss直聘请求失败，状态码: {response.status}")
                
        except Exception as e:
            crawler_logger.error(f"Boss直聘爬取异常: {e}")
        
        return jobs
    
    def _parse_boss_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析Boss直聘职位数据"""
        try:
            job = {
                "title": job_data.get("jobName", ""),
                "company": job_data.get("brandName", ""),
                "location": f"{job_data.get('cityName', '')}-{job_data.get('areaDistrict', '')}",
                "salary": job_data.get("salaryDesc", ""),
                "experience": job_data.get("jobExperience", ""),
                "education": job_data.get("jobDegree", ""),
                "description": job_data.get("jobDesc", ""),
                "requirements": ", ".join(job_data.get("skills", [])),
                "benefits": ", ".join(job_data.get("jobLabels", [])),
                "job_url": f"https://www.zhipin.com/job_detail/{job_data.get('encryptJobId', '')}.html",
                "company_url": f"https://www.zhipin.com/gongsi/{job_data.get('encryptBrandId', '')}.html",
                "publish_time": job_data.get("lastModifyTime", ""),
                "job_type": "全职",
                "company_size": job_data.get("brandScaleName", ""),
                "company_industry": job_data.get("brandIndustry", ""),
                "boss_name": job_data.get("bossName", ""),
                "boss_title": job_data.get("bossTitle", ""),
                "source_website": "boss"
            }
            
            return job
            
        except Exception as e:
            crawler_logger.error(f"解析Boss直聘职位数据失败: {e}")
            return None
    
    async def crawl_all_cities(self, keyword: str, max_pages_per_city: int = 50) -> List[Dict[str, Any]]:
        """爬取所有城市的职位数据"""
        all_jobs = []
        
        for city_name, city_code in self.city_codes.items():
            crawler_logger.info(f"开始爬取Boss直聘 {city_name} 的职位数据")
            
            page = 1
            consecutive_empty_pages = 0
            
            while page <= max_pages_per_city and consecutive_empty_pages < 3:
                try:
                    jobs = await self._fetch_boss_jobs(keyword, page, city_code)
                    
                    if jobs:
                        all_jobs.extend(jobs)
                        consecutive_empty_pages = 0
                        crawler_logger.info(f"Boss直聘 {city_name} 第{page}页: {len(jobs)}条职位")
                    else:
                        consecutive_empty_pages += 1
                        crawler_logger.warning(f"Boss直聘 {city_name} 第{page}页: 无数据")
                    
                    page += 1
                    
                    # 随机延迟
                    await asyncio.sleep(random.uniform(2, 4))
                    
                except Exception as e:
                    crawler_logger.error(f"Boss直聘 {city_name} 第{page}页爬取失败: {e}")
                    consecutive_empty_pages += 1
                    page += 1
            
            crawler_logger.info(f"Boss直聘 {city_name} 爬取完成")
            
            # 城市间延迟
            await asyncio.sleep(random.uniform(3, 6))
        
        return all_jobs
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 全面覆盖"""
        search_url = self._build_search_url("professional_boss://", page)
        crawler_logger.info(f"专业Boss直聘爬虫启动: {search_url}")
        
        # 如果是第一页，进行全面爬取
        if page == 1:
            all_jobs = await self.crawl_all_cities(self.keyword, max_pages_per_city=20)
            crawler_logger.info(f"Boss直聘全面爬取完成，共获取 {len(all_jobs)} 条职位")
            return all_jobs
        else:
            # 单页爬取
            jobs = await self._fetch_boss_jobs(self.keyword, page)
            return jobs
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'professional_boss',
            'keyword': self.keyword,
            'base_url': 'https://www.zhipin.com',
            'search_url': f'professional_boss://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'professional_comprehensive_crawler',
            'cities_covered': len(self.city_codes),
            'api_endpoints': len(self.api_endpoints),
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
