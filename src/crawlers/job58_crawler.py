"""
58同城爬虫
58.com crawler
"""

import re
from typing import Dict, Any, List
from urllib.parse import urljoin

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class Job58Crawler(BaseCrawler):
    """58同城爬虫"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("job58", keyword)
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取58同城默认提取模式"""
        return {
            "name": "58Job Jobs",
            "baseSelector": ".job-list .job-item, .list .item, .job-card",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-title a, .job-name a, .title a",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name a, .comp-name, .company",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-area, .address, .location",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".job-salary, .salary, .price",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".job-require, .require, .experience",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".job-require, .require, .education",
                    "type": "text"
                },
                {
                    "name": "job_url",
                    "selector": ".job-title a, .job-name a, .title a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "company_url",
                    "selector": ".company-name a, .comp-name a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "publish_time",
                    "selector": ".job-time, .time, .date",
                    "type": "text"
                },
                {
                    "name": "job_type",
                    "selector": ".job-type, .type",
                    "type": "text"
                }
            ]
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建58同城搜索URL"""
        # 58同城的分页参数通常是pn
        if page == 1:
            return base_url
        
        separator = "&" if "?" in base_url else "?"
        return f"{base_url}{separator}pn={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取58同城职位详情页"""
        if not job_url:
            return {}
        
        # 确保URL是完整的
        if job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        
        crawler_logger.debug(f"爬取职位详情: {job_url}")
        
        # 职位详情页的提取模式
        detail_schema = {
            "name": "58Job Job Detail",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-title h1, .job-name, .title",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name, .comp-name",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".job-salary, .salary, .price",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-location, .address",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".job-require .experience, .require-item",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".job-require .education, .require-item",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": ".job-description, .job-detail, .content",
                    "type": "text"
                },
                {
                    "name": "requirements",
                    "selector": ".job-requirements, .require-detail",
                    "type": "text"
                },
                {
                    "name": "benefits",
                    "selector": ".job-benefits, .welfare",
                    "type": "text"
                },
                {
                    "name": "contact_info",
                    "selector": ".contact-info, .contact",
                    "type": "text"
                }
            ]
        }
        
        # 使用详情页模式爬取
        detail_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(detail_schema),
            js_code=anti_detection.get_js_stealth_code(),
            verbose=True
        )
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                result = await crawler.arun(url=job_url, config=detail_config)
                
                if result.success:
                    detail_data = self._parse_extracted_data(result.extracted_content)
                    if detail_data:
                        return detail_data[0] if isinstance(detail_data, list) else detail_data
                
        except Exception as e:
            crawler_logger.error(f"爬取职位详情失败: {e}")
        
        return {}
    
    def _clean_58_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗58同城特有的数据格式"""
        cleaned = {}
        
        # 职位标题
        title = raw_data.get('title', '').strip()
        # 移除58同城特有的标记
        title = re.sub(r'[\r\n\t]+', ' ', title)
        title = re.sub(r'\s*\[.*?\]\s*', '', title)  # 移除方括号内容
        title = re.sub(r'\s*（.*?）\s*', '', title)  # 移除圆括号内容
        cleaned['title'] = title
        
        # 公司名称
        company = raw_data.get('company', '').strip()
        company = re.sub(r'[\r\n\t]+', ' ', company)
        # 移除58同城公司名称中的认证标识
        company = re.sub(r'\s*认证\s*', '', company)
        cleaned['company'] = company
        
        # 工作地点
        location = raw_data.get('location', '').strip()
        location = re.sub(r'[\r\n\t]+', ' ', location)
        # 58同城地点格式处理
        location = re.sub(r'地址[：:]?', '', location)
        location = re.sub(r'工作地点[：:]?', '', location)
        cleaned['location'] = location
        
        # 薪资处理
        salary = raw_data.get('salary', '').strip()
        salary = re.sub(r'[\r\n\t]+', ' ', salary)
        salary = re.sub(r'薪资[：:]?', '', salary)
        salary = re.sub(r'工资[：:]?', '', salary)
        # 58同城薪资格式特殊处理
        salary = re.sub(r'[^\d\-/KkWw万元千·\s]', '', salary)
        cleaned['salary'] = salary
        
        # 工作经验和学历要求
        require_text = raw_data.get('experience', '') + ' ' + raw_data.get('education', '')
        require_text = require_text.strip()
        
        # 分离经验和学历
        experience = ''
        education = ''
        
        if require_text:
            # 查找经验相关关键词
            exp_patterns = [
                r'(\d+[-~]\d+年)', r'(\d+年以上)', r'(\d+年经验)',
                r'(无经验)', r'(应届)', r'(实习)', r'(经验不限)'
            ]
            for pattern in exp_patterns:
                match = re.search(pattern, require_text)
                if match:
                    experience = match.group(1)
                    break
            
            # 查找学历相关关键词
            edu_patterns = [
                r'(博士)', r'(硕士)', r'(本科)', r'(大专)', r'(中专)',
                r'(高中)', r'(初中)', r'(学历不限)'
            ]
            for pattern in edu_patterns:
                match = re.search(pattern, require_text)
                if match:
                    education = match.group(1)
                    break
        
        cleaned['experience'] = experience
        cleaned['education'] = education
        
        # 职位描述
        description = raw_data.get('description', '').strip()
        description = re.sub(r'[\r\n\t]+', ' ', description)
        # 移除58同城描述中的联系方式
        description = re.sub(r'联系电话.*?$', '', description)
        description = re.sub(r'微信.*?$', '', description)
        cleaned['description'] = description
        
        # 职位要求
        requirements = raw_data.get('requirements', '').strip()
        requirements = re.sub(r'[\r\n\t]+', ' ', requirements)
        cleaned['requirements'] = requirements
        
        # 福利待遇
        benefits = raw_data.get('benefits', '').strip()
        benefits = re.sub(r'[\r\n\t]+', ' ', benefits)
        cleaned['benefits'] = benefits
        
        # 职位类型
        job_type = raw_data.get('job_type', '').strip()
        cleaned['job_type'] = job_type
        
        # URL处理
        job_url = raw_data.get('job_url', '')
        if job_url and job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        cleaned['job_url'] = job_url
        
        return cleaned
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """重写搜索页面爬取，添加58同城特有的处理"""
        jobs_data = await super().crawl_search_page(page)
        
        # 对58同城数据进行特殊清洗
        cleaned_jobs = []
        for job_data in jobs_data:
            cleaned_job = self._clean_58_data(job_data)
            if cleaned_job.get('title') and cleaned_job.get('company'):
                cleaned_jobs.append(cleaned_job)
        
        return cleaned_jobs
    
    def _extract_page_info(self, html: str) -> Dict[str, Any]:
        """提取页面信息（总页数、当前页等）"""
        page_info = {
            'current_page': 1,
            'total_pages': 1,
            'total_jobs': 0
        }
        
        try:
            import re
            
            # 查找总页数 - 58同城的分页信息
            total_pages_patterns = [
                r'共(\d+)页',
                r'总共(\d+)页',
                r'/(\d+)页'
            ]
            
            for pattern in total_pages_patterns:
                match = re.search(pattern, html)
                if match:
                    page_info['total_pages'] = int(match.group(1))
                    break
            
            # 查找总职位数
            total_jobs_patterns = [
                r'共(\d+)个职位',
                r'找到(\d+)个相关职位',
                r'共(\d+)条信息',
                r'(\d+)个招聘信息'
            ]
            
            for pattern in total_jobs_patterns:
                match = re.search(pattern, html)
                if match:
                    page_info['total_jobs'] = int(match.group(1))
                    break
                    
        except Exception as e:
            crawler_logger.debug(f"提取页面信息失败: {e}")
        
        return page_info
    
    def _filter_valid_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤有效的职位信息"""
        valid_jobs = []
        
        for job in jobs:
            # 58同城可能包含很多非正式职位，需要过滤
            title = job.get('title', '').lower()
            
            # 过滤掉明显不相关的职位
            invalid_keywords = [
                '兼职', '小时工', '临时工', '代理', '销售',
                '客服', '话务员', '推广', '刷单', '打字员'
            ]
            
            # 检查是否包含无效关键词
            is_valid = True
            for keyword in invalid_keywords:
                if keyword in title:
                    is_valid = False
                    break
            
            if is_valid and job.get('title') and job.get('company'):
                valid_jobs.append(job)
        
        return valid_jobs
