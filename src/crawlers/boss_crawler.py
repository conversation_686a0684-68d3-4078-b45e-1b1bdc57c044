"""
Boss直聘爬虫
Boss.com crawler
"""

import re
from typing import Dict, Any, List
from urllib.parse import urljoin, quote

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class <PERSON><PERSON>raw<PERSON>(BaseCrawler):
    """Boss直聘爬虫"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("boss", keyword)
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取Boss直聘默认提取模式"""
        return {
            "name": "Boss Jobs",
            "baseSelector": ".job-list li, .job-card-wrapper, .job-card-left",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-title, .job-name, .position-name",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name, .company-text",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-area, .job-limit-item-left",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".salary, .red",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".job-limit-item-left, .job-limit .gray",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".job-limit-item-left, .job-limit .gray",
                    "type": "text"
                },
                {
                    "name": "job_url",
                    "selector": ".job-title a, .position-name a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "company_url",
                    "selector": ".company-name a, .company-text a",
                    "type": "attribute", 
                    "attribute": "href"
                },
                {
                    "name": "description",
                    "selector": ".job-desc, .job-detail-text, .position-desc",
                    "type": "text"
                }
            ]
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建Boss直聘搜索URL"""
        # Boss直聘的分页参数通常是page
        if "?" in base_url:
            separator = "&"
        else:
            separator = "?"
        
        return f"{base_url}{separator}page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取Boss直聘职位详情页"""
        if not job_url:
            return {}
        
        # 确保URL是完整的
        if job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        
        crawler_logger.debug(f"爬取职位详情: {job_url}")
        
        # 职位详情页的提取模式
        detail_schema = {
            "name": "Boss Job Detail",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-title, .position-name h1",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name, .company-info h3",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".salary, .job-primary .red",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-location, .job-primary .gray",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".job-primary .gray",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".job-primary .gray",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": ".job-detail-section, .job-sec-text",
                    "type": "text"
                },
                {
                    "name": "requirements",
                    "selector": ".job-require, .job-detail-section",
                    "type": "text"
                },
                {
                    "name": "benefits",
                    "selector": ".job-tag-list, .position-label",
                    "type": "text"
                }
            ]
        }
        
        # 使用详情页模式爬取
        detail_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(detail_schema),
            js_code=anti_detection.get_js_stealth_code(),
            verbose=True
        )
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                result = await crawler.arun(url=job_url, config=detail_config)
                
                if result.success:
                    detail_data = self._parse_extracted_data(result.extracted_content)
                    if detail_data:
                        return detail_data[0] if isinstance(detail_data, list) else detail_data
                
        except Exception as e:
            crawler_logger.error(f"爬取职位详情失败: {e}")
        
        return {}
    
    def _clean_boss_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗Boss直聘特有的数据格式"""
        cleaned = {}
        
        # 职位标题
        title = raw_data.get('title', '').strip()
        cleaned['title'] = re.sub(r'[\r\n\t]+', ' ', title)
        
        # 公司名称
        company = raw_data.get('company', '').strip()
        cleaned['company'] = re.sub(r'[\r\n\t]+', ' ', company)
        
        # 工作地点
        location = raw_data.get('location', '').strip()
        # Boss直聘地点格式通常是 "城市·区域"
        location = re.sub(r'[\r\n\t]+', ' ', location)
        cleaned['location'] = location
        
        # 薪资处理
        salary = raw_data.get('salary', '').strip()
        # 移除多余的空白字符和特殊符号
        salary = re.sub(r'[\r\n\t]+', ' ', salary)
        cleaned['salary'] = salary
        
        # 工作经验
        experience = raw_data.get('experience', '').strip()
        cleaned['experience'] = re.sub(r'[\r\n\t]+', ' ', experience)
        
        # 学历要求
        education = raw_data.get('education', '').strip()
        cleaned['education'] = re.sub(r'[\r\n\t]+', ' ', education)
        
        # 职位描述
        description = raw_data.get('description', '').strip()
        description = re.sub(r'[\r\n\t]+', ' ', description)
        cleaned['description'] = description
        
        # 职位要求
        requirements = raw_data.get('requirements', '').strip()
        requirements = re.sub(r'[\r\n\t]+', ' ', requirements)
        cleaned['requirements'] = requirements
        
        # 福利待遇
        benefits = raw_data.get('benefits', '').strip()
        benefits = re.sub(r'[\r\n\t]+', ' ', benefits)
        cleaned['benefits'] = benefits
        
        # URL处理
        job_url = raw_data.get('job_url', '')
        if job_url and job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        cleaned['job_url'] = job_url
        
        return cleaned
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """重写搜索页面爬取，添加Boss直聘特有的处理"""
        jobs_data = await super().crawl_search_page(page)
        
        # 对Boss直聘数据进行特殊清洗
        cleaned_jobs = []
        for job_data in jobs_data:
            cleaned_job = self._clean_boss_data(job_data)
            if cleaned_job.get('title') and cleaned_job.get('company'):
                cleaned_jobs.append(cleaned_job)
        
        return cleaned_jobs
