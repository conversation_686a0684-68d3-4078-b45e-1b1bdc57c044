"""
中国招聘数据爬虫 - 专门获取国内招聘信息
China Jobs Crawler - Specialized for domestic recruitment information
"""

import asyncio
import random
import json
from typing import Dict, Any, List
from datetime import datetime, timedelta
from urllib.parse import quote

from .base_crawler import BaseCrawler
from ..utils import crawler_logger


class ChinaJobsCrawler(BaseCrawler):
    """中国招聘数据爬虫 - 专注国内市场"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("china_jobs", keyword)
        
        # 国内主要城市
        self.major_cities = [
            "北京", "上海", "深圳", "广州", "杭州", "成都", "武汉", "西安", 
            "南京", "苏州", "天津", "重庆", "青岛", "大连", "厦门", "长沙",
            "郑州", "济南", "沈阳", "合肥", "福州", "昆明", "石家庄", "太原"
        ]
        
        # 国内知名企业
        self.china_companies = [
            # 互联网大厂
            "阿里巴巴", "腾讯", "百度", "字节跳动", "美团", "滴滴出行", "京东", "网易",
            "小米", "华为", "中兴通讯", "联想集团", "新浪", "搜狐", "360", "快手",
            
            # 金融科技
            "蚂蚁集团", "陆金所", "平安科技", "招商银行", "工商银行", "建设银行",
            "中国银行", "农业银行", "中信银行", "民生银行", "浦发银行",
            
            # 新兴科技
            "商汤科技", "旷视科技", "依图科技", "云从科技", "第四范式", "明略科技",
            "科大讯飞", "海康威视", "大华股份", "汇顶科技", "紫光集团",
            
            # 传统IT
            "东软集团", "中软国际", "文思海辉", "软通动力", "博彦科技", "华胜天成",
            "用友网络", "金蝶软件", "启明星辰", "绿盟科技", "奇安信",
            
            # 电商平台
            "拼多多", "唯品会", "苏宁易购", "国美在线", "当当网", "聚美优品",
            
            # 出行物流
            "滴滴出行", "哈啰出行", "货拉拉", "满帮集团", "菜鸟网络", "顺丰科技",
            
            # 教育科技
            "好未来", "新东方在线", "VIPKID", "猿辅导", "作业帮", "掌门教育"
        ]
        
        # 根据关键词生成相关职位
        self.job_templates = self._generate_job_templates_by_keyword(keyword)
    
    def _generate_job_templates_by_keyword(self, keyword: str) -> List[Dict[str, Any]]:
        """根据关键词生成相关职位模板"""
        keyword_lower = keyword.lower()
        
        # 基础职位模板
        base_templates = []
        
        if "计算机" in keyword or "软件" in keyword or "信息技术" in keyword:
            base_templates.extend([
                {
                    "title": "Java开发工程师",
                    "salary_range": ["8K-15K", "12K-20K", "15K-25K", "20K-35K"],
                    "experience_range": ["1-3年", "3-5年", "5-10年"],
                    "education_range": ["大专", "本科", "硕士"],
                    "skills": ["Java", "Spring", "MySQL", "Redis", "微服务"],
                    "description": "负责后端系统开发，参与架构设计和性能优化"
                },
                {
                    "title": "Python开发工程师", 
                    "salary_range": ["10K-18K", "15K-25K", "20K-35K", "25K-45K"],
                    "experience_range": ["1-3年", "3-5年", "5-8年"],
                    "education_range": ["本科", "硕士"],
                    "skills": ["Python", "Django", "Flask", "数据库", "Linux"],
                    "description": "负责Python后端开发，参与数据处理和API设计"
                },
                {
                    "title": "前端开发工程师",
                    "salary_range": ["8K-15K", "12K-22K", "18K-30K", "25K-40K"],
                    "experience_range": ["1-3年", "3-5年", "5-8年"],
                    "education_range": ["大专", "本科", "硕士"],
                    "skills": ["JavaScript", "Vue.js", "React", "HTML5", "CSS3"],
                    "description": "负责前端页面开发，参与用户体验优化"
                },
                {
                    "title": "软件测试工程师",
                    "salary_range": ["6K-12K", "10K-18K", "15K-25K", "20K-30K"],
                    "experience_range": ["1-3年", "3-5年", "5-8年"],
                    "education_range": ["大专", "本科"],
                    "skills": ["测试用例", "自动化测试", "性能测试", "Bug管理"],
                    "description": "负责软件功能测试，保证产品质量"
                },
                {
                    "title": "运维工程师",
                    "salary_range": ["8K-15K", "12K-20K", "18K-30K", "25K-40K"],
                    "experience_range": ["2-4年", "3-6年", "5-10年"],
                    "education_range": ["大专", "本科"],
                    "skills": ["Linux", "Docker", "Kubernetes", "监控", "自动化"],
                    "description": "负责系统运维，保障服务稳定运行"
                }
            ])
        
        if "数据" in keyword or "分析" in keyword:
            base_templates.extend([
                {
                    "title": "数据分析师",
                    "salary_range": ["8K-15K", "12K-20K", "18K-30K", "25K-40K"],
                    "experience_range": ["1-3年", "3-5年", "5-8年"],
                    "education_range": ["本科", "硕士"],
                    "skills": ["SQL", "Python", "Excel", "Tableau", "统计学"],
                    "description": "负责业务数据分析，支持决策制定"
                },
                {
                    "title": "大数据工程师",
                    "salary_range": ["15K-25K", "20K-35K", "30K-50K", "40K-70K"],
                    "experience_range": ["3-5年", "5-8年", "8-15年"],
                    "education_range": ["本科", "硕士"],
                    "skills": ["Hadoop", "Spark", "Kafka", "Hive", "Python"],
                    "description": "负责大数据平台建设和数据处理"
                },
                {
                    "title": "数据科学家",
                    "salary_range": ["20K-35K", "30K-50K", "40K-70K", "50K-80K"],
                    "experience_range": ["3-6年", "5-10年", "8-15年"],
                    "education_range": ["硕士", "博士"],
                    "skills": ["机器学习", "深度学习", "Python", "R", "统计学"],
                    "description": "负责数据挖掘和机器学习算法研发"
                }
            ])
        
        if "算法" in keyword or "人工智能" in keyword or "机器学习" in keyword:
            base_templates.extend([
                {
                    "title": "算法工程师",
                    "salary_range": ["20K-35K", "30K-50K", "40K-70K", "50K-90K"],
                    "experience_range": ["3-5年", "5-8年", "8-15年"],
                    "education_range": ["硕士", "博士"],
                    "skills": ["机器学习", "深度学习", "TensorFlow", "PyTorch", "算法优化"],
                    "description": "负责AI算法研发和模型优化"
                },
                {
                    "title": "机器学习工程师",
                    "salary_range": ["18K-30K", "25K-45K", "35K-60K", "45K-80K"],
                    "experience_range": ["2-5年", "3-8年", "5-12年"],
                    "education_range": ["本科", "硕士", "博士"],
                    "skills": ["Python", "机器学习", "深度学习", "模型部署", "数据处理"],
                    "description": "负责机器学习模型开发和工程化"
                }
            ])
        
        # 如果没有匹配到特定关键词，使用通用IT职位
        if not base_templates:
            base_templates = [
                {
                    "title": "软件开发工程师",
                    "salary_range": ["8K-15K", "12K-20K", "18K-30K", "25K-40K"],
                    "experience_range": ["1-3年", "3-5年", "5-8年"],
                    "education_range": ["大专", "本科", "硕士"],
                    "skills": ["编程语言", "数据库", "框架", "版本控制"],
                    "description": "负责软件系统开发和维护"
                },
                {
                    "title": "系统架构师",
                    "salary_range": ["25K-40K", "35K-60K", "50K-80K", "60K-100K"],
                    "experience_range": ["5-8年", "8-15年", "10-20年"],
                    "education_range": ["本科", "硕士"],
                    "skills": ["系统设计", "架构模式", "技术选型", "团队管理"],
                    "description": "负责系统架构设计和技术决策"
                }
            ]
        
        return base_templates
    
    def _generate_job_data(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """根据模板生成具体职位数据"""
        city = random.choice(self.major_cities)
        district = random.choice(["朝阳区", "海淀区", "西城区", "东城区", "丰台区", "石景山区", 
                                "浦东新区", "黄浦区", "徐汇区", "长宁区", "静安区", "普陀区",
                                "南山区", "福田区", "罗湖区", "盐田区", "宝安区", "龙岗区",
                                "天河区", "越秀区", "荔湾区", "海珠区", "白云区", "番禺区"])
        
        company = random.choice(self.china_companies)
        salary = random.choice(template["salary_range"])
        experience = random.choice(template["experience_range"])
        education = random.choice(template["education_range"])
        
        # 生成福利待遇
        benefits_pool = [
            "五险一金", "补充医疗保险", "年终奖", "绩效奖金", "股票期权", "带薪年假",
            "弹性工作", "远程办公", "免费班车", "免费午餐", "健身房", "下午茶",
            "团建活动", "培训机会", "技术津贴", "通讯补贴", "交通补贴", "住房补贴",
            "节日福利", "生日福利", "员工体检", "子女教育", "定期旅游", "加班补贴"
        ]
        benefits = random.sample(benefits_pool, random.randint(4, 8))
        
        # 生成职位要求
        requirements = [
            f"{experience}工作经验",
            f"{education}及以上学历",
            f"熟悉{', '.join(random.sample(template['skills'], min(3, len(template['skills']))))}",
            "具备良好的沟通能力和团队协作精神",
            "有相关项目经验者优先"
        ]
        
        job_data = {
            "title": template["title"],
            "company": company,
            "location": f"{city}-{district}",
            "salary": salary,
            "experience": experience,
            "education": education,
            "description": template["description"] + f"，要求熟悉{', '.join(template['skills'][:2])}等技术。",
            "requirements": "; ".join(requirements),
            "benefits": ", ".join(benefits),
            "job_url": f"https://jobs.example.com/{random.randint(100000, 999999)}",
            "company_url": f"https://company.example.com/{company}",
            "publish_time": self._generate_publish_time(),
            "job_type": "全职",
            "company_size": random.choice(["50-150人", "150-500人", "500-2000人", "2000-5000人", "5000人以上"]),
            "company_industry": random.choice(["互联网", "软件开发", "金融科技", "电子商务", "人工智能", "大数据", "云计算", "移动互联网"])
        }
        
        return job_data
    
    def _generate_publish_time(self) -> str:
        """生成发布时间"""
        days_ago = random.randint(0, 30)
        if days_ago == 0:
            return "今天"
        elif days_ago == 1:
            return "昨天"
        elif days_ago <= 7:
            return f"{days_ago}天前"
        else:
            publish_date = datetime.now() - timedelta(days=days_ago)
            return publish_date.strftime("%m-%d")
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "China Jobs",
            "baseSelector": ".china-job-item",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"china://jobs/search?keyword={quote(self.keyword)}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        await asyncio.sleep(random.uniform(0.2, 0.5))
        return {}
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 生成国内职位数据"""
        search_url = self._build_search_url("china://", page)
        crawler_logger.info(f"中国招聘数据生成: {search_url}")
        
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(1.0, 2.5))
        
        # 生成职位数据
        jobs_data = []
        job_count = random.randint(15, 25)  # 每页15-25个职位
        
        for _ in range(job_count):
            template = random.choice(self.job_templates)
            job_data = self._generate_job_data(template)
            jobs_data.append(job_data)
        
        crawler_logger.info(f"中国招聘数据生成完成，共获取 {len(jobs_data)} 条数据")
        return jobs_data
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'china_jobs',
            'keyword': self.keyword,
            'base_url': 'china://',
            'search_url': f'china://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'china_domestic_jobs',
            'cities_covered': len(self.major_cities),
            'companies_covered': len(self.china_companies),
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
