"""
专业前程无忧爬虫 - 获取全部真实招聘信息
Professional 51job Crawler - Get all real recruitment information
"""

import asyncio
import aiohttp
import json
import random
import time
from typing import Dict, Any, List
from urllib.parse import quote, urljoin
from datetime import datetime
import re

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class Professional51jobCrawler(BaseCrawler):
    """专业前程无忧爬虫 - 无遗漏数据获取"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("professional_51job", keyword)
        
        # 前程无忧API端点
        self.api_endpoints = {
            "search": "https://search.51job.com/list/000000,000000,0000,00,9,99,{keyword},2,{page}.html",
            "api_search": "https://cubes.51job.com/api/search/search",
            "detail": "https://jobs.51job.com/",
            "company": "https://company.51job.com/"
        }
        
        # 城市代码映射
        self.city_codes = {
            "全国": "000000",
            "北京": "010000", "上海": "020000", "广州": "030200", "深圳": "040000",
            "杭州": "080200", "成都": "090200", "武汉": "180200", "西安": "200200",
            "南京": "070200", "苏州": "070300", "天津": "050000", "重庆": "060000",
            "青岛": "120300", "大连": "230200", "厦门": "110300", "长沙": "160200",
            "郑州": "170200", "济南": "120200", "沈阳": "230300", "合肥": "150200"
        }
        
        # 工作年限代码
        self.workyear_codes = {
            "不限": "99", "无工作经验": "01", "1年经验": "02", "2年经验": "03",
            "3-5年经验": "04", "6-7年经验": "05", "8-10年经验": "06", "10年以上经验": "07"
        }
        
        # 学历代码
        self.degree_codes = {
            "不限": "99", "初中及以下": "01", "高中/中专/技校": "02",
            "大专": "03", "本科": "04", "硕士": "05", "博士": "06"
        }
        
        # 请求头配置
        self.headers_pool = [
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Referer": "https://www.51job.com/",
                "Upgrade-Insecure-Requests": "1",
                "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-User": "?1"
            }
        ]
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "Professional 51job Jobs",
            "baseSelector": ".professional-51job-job",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"professional_51job://search?keyword={quote(self.keyword)}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        await asyncio.sleep(random.uniform(0.5, 1.5))
        return {}
    
    def _build_search_params(self, keyword: str, page: int, city_code: str = "000000") -> Dict[str, Any]:
        """构建搜索参数"""
        return {
            "api_key": "51job",
            "timestamp": int(time.time() * 1000),
            "keyword": keyword,
            "searchType": "2",
            "function": "",
            "industry": "",
            "jobArea": city_code,
            "jobArea2": "",
            "landmark": "",
            "metro": "",
            "salary": "",
            "workYear": "99",
            "degree": "99",
            "companyType": "",
            "companySize": "",
            "jobType": "",
            "issueDate": "",
            "sortType": "0",
            "pageNum": str(page),
            "requestId": "",
            "pageSize": "50",
            "source": "1",
            "accountId": "",
            "pageCode": "sou|sou|soulb"
        }
    
    async def _fetch_51job_jobs_html(self, keyword: str, page: int = 1, city_code: str = "000000") -> List[Dict[str, Any]]:
        """通过HTML页面获取前程无忧职位数据"""
        jobs = []
        
        try:
            headers = random.choice(self.headers_pool).copy()
            headers.update(anti_detection.get_headers())
            
            # 构建搜索URL
            search_url = f"https://search.51job.com/list/{city_code},000000,0000,00,9,99,{quote(keyword)},2,{page}.html"
            
            async with aiohttp.ClientSession(headers=headers) as session:
                async with session.get(search_url) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        jobs = self._parse_51job_html(html_content)
                        crawler_logger.info(f"前程无忧第{page}页获取到 {len(jobs)} 条职位")
                    else:
                        crawler_logger.error(f"前程无忧请求失败，状态码: {response.status}")
                
        except Exception as e:
            crawler_logger.error(f"前程无忧HTML爬取异常: {e}")
        
        return jobs
    
    def _parse_51job_html(self, html_content: str) -> List[Dict[str, Any]]:
        """解析前程无忧HTML页面"""
        jobs = []
        
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找职位列表
            job_elements = soup.find_all('div', class_='e') or soup.find_all('tr', class_='odd') or soup.find_all('tr', class_='even')
            
            for job_elem in job_elements:
                try:
                    job = self._extract_job_from_element(job_elem)
                    if job:
                        jobs.append(job)
                except Exception as e:
                    continue
            
        except Exception as e:
            crawler_logger.error(f"解析前程无忧HTML失败: {e}")
        
        return jobs
    
    def _extract_job_from_element(self, job_elem) -> Dict[str, Any]:
        """从HTML元素中提取职位信息"""
        try:
            # 职位标题和链接
            title_elem = job_elem.find('a', class_='jname') or job_elem.find('span', class_='jname')
            title = title_elem.get_text(strip=True) if title_elem else ""
            job_url = title_elem.get('href', '') if title_elem and title_elem.get('href') else ""
            
            # 公司名称
            company_elem = job_elem.find('a', class_='cname') or job_elem.find('span', class_='cname')
            company = company_elem.get_text(strip=True) if company_elem else ""
            
            # 工作地点
            location_elem = job_elem.find('span', class_='lname') or job_elem.find('td', class_='l2')
            location = location_elem.get_text(strip=True) if location_elem else ""
            
            # 薪资
            salary_elem = job_elem.find('span', class_='sal') or job_elem.find('td', class_='l4')
            salary = salary_elem.get_text(strip=True) if salary_elem else ""
            
            # 发布时间
            time_elem = job_elem.find('span', class_='time') or job_elem.find('td', class_='l5')
            publish_time = time_elem.get_text(strip=True) if time_elem else ""
            
            # 工作经验和学历
            info_elem = job_elem.find('span', class_='info') or job_elem.find('td', class_='l3')
            info_text = info_elem.get_text(strip=True) if info_elem else ""
            
            # 解析经验和学历
            experience, education = self._parse_experience_education(info_text)
            
            job = {
                "title": title,
                "company": company,
                "location": location,
                "salary": salary,
                "experience": experience,
                "education": education,
                "description": "",
                "requirements": info_text,
                "benefits": "",
                "job_url": urljoin("https://jobs.51job.com/", job_url) if job_url else "",
                "company_url": "",
                "publish_time": publish_time,
                "job_type": "全职",
                "company_size": "",
                "company_industry": "",
                "source_website": "51job"
            }
            
            return job if title and company else None
            
        except Exception as e:
            return None
    
    def _parse_experience_education(self, info_text: str) -> tuple:
        """解析经验和学历信息"""
        experience = ""
        education = ""
        
        # 经验匹配
        exp_patterns = [
            r'(\d+[-~]\d+年)', r'(\d+年以上)', r'(无工作经验)', r'(应届毕业生)',
            r'(不限经验)', r'(\d+年)'
        ]
        
        for pattern in exp_patterns:
            match = re.search(pattern, info_text)
            if match:
                experience = match.group(1)
                break
        
        # 学历匹配
        edu_patterns = [
            r'(博士)', r'(硕士)', r'(本科)', r'(大专)', r'(高中)', r'(中专)', r'(初中)',
            r'(不限学历)'
        ]
        
        for pattern in edu_patterns:
            match = re.search(pattern, info_text)
            if match:
                education = match.group(1)
                break
        
        return experience, education
    
    async def crawl_all_cities(self, keyword: str, max_pages_per_city: int = 50) -> List[Dict[str, Any]]:
        """爬取所有城市的职位数据"""
        all_jobs = []
        
        for city_name, city_code in self.city_codes.items():
            crawler_logger.info(f"开始爬取前程无忧 {city_name} 的职位数据")
            
            page = 1
            consecutive_empty_pages = 0
            
            while page <= max_pages_per_city and consecutive_empty_pages < 3:
                try:
                    jobs = await self._fetch_51job_jobs_html(keyword, page, city_code)
                    
                    if jobs:
                        all_jobs.extend(jobs)
                        consecutive_empty_pages = 0
                        crawler_logger.info(f"前程无忧 {city_name} 第{page}页: {len(jobs)}条职位")
                    else:
                        consecutive_empty_pages += 1
                        crawler_logger.warning(f"前程无忧 {city_name} 第{page}页: 无数据")
                    
                    page += 1
                    
                    # 随机延迟
                    await asyncio.sleep(random.uniform(2, 4))
                    
                except Exception as e:
                    crawler_logger.error(f"前程无忧 {city_name} 第{page}页爬取失败: {e}")
                    consecutive_empty_pages += 1
                    page += 1
            
            crawler_logger.info(f"前程无忧 {city_name} 爬取完成")
            
            # 城市间延迟
            await asyncio.sleep(random.uniform(3, 6))
        
        return all_jobs
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 全面覆盖"""
        search_url = self._build_search_url("professional_51job://", page)
        crawler_logger.info(f"专业前程无忧爬虫启动: {search_url}")
        
        # 如果是第一页，进行全面爬取
        if page == 1:
            all_jobs = await self.crawl_all_cities(self.keyword, max_pages_per_city=20)
            crawler_logger.info(f"前程无忧全面爬取完成，共获取 {len(all_jobs)} 条职位")
            return all_jobs
        else:
            # 单页爬取
            jobs = await self._fetch_51job_jobs_html(self.keyword, page)
            return jobs
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'professional_51job',
            'keyword': self.keyword,
            'base_url': 'https://www.51job.com',
            'search_url': f'professional_51job://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'professional_comprehensive_crawler',
            'cities_covered': len(self.city_codes),
            'api_endpoints': len(self.api_endpoints),
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
