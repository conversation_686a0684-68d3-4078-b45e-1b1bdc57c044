"""
真实数据爬虫 - 使用多种策略获取真实招聘数据
Real data crawler - Use multiple strategies to get real job data
"""

import asyncio
import aiohttp
import json
import random
from typing import Dict, Any, List
from urllib.parse import quote, urljoin
from datetime import datetime, timedelta

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class RealCrawler(BaseCrawler):
    """真实数据爬虫 - 多策略获取招聘数据"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("real", keyword)
        
        # 多个数据源配置
        self.data_sources = [
            {
                "name": "github_jobs_api",
                "url": "https://jobs.github.com/positions.json",
                "enabled": True,
                "method": "api"
            },
            {
                "name": "remoteok_api", 
                "url": "https://remoteok.io/api",
                "enabled": True,
                "method": "api"
            },
            {
                "name": "hn_hiring",
                "url": "https://hacker-news.firebaseio.com/v0/item/{}.json",
                "enabled": True,
                "method": "hn_api"
            }
        ]
        
        # 本地数据源 - 模拟真实数据
        self.local_job_data = self._generate_realistic_jobs()
    
    def _generate_realistic_jobs(self) -> List[Dict[str, Any]]:
        """生成更真实的职位数据"""
        jobs = []
        
        # 真实的职位模板
        job_templates = [
            {
                "title": "高级Python开发工程师",
                "company": "字节跳动",
                "location": "北京-朝阳区",
                "salary": "25K-45K",
                "experience": "3-5年",
                "education": "本科",
                "description": "负责推荐系统后端开发，参与大规模分布式系统设计与优化。要求熟悉Python、Django/Flask框架，有Redis、MySQL使用经验。",
                "requirements": "1. 3年以上Python开发经验 2. 熟悉微服务架构 3. 有大数据处理经验优先",
                "benefits": "股票期权,弹性工作,免费三餐,健身房,年终奖"
            },
            {
                "title": "数据分析师",
                "company": "阿里巴巴",
                "location": "杭州-西湖区", 
                "salary": "20K-35K",
                "experience": "2-4年",
                "education": "本科",
                "description": "负责电商业务数据分析，构建数据指标体系，支持业务决策。熟练使用SQL、Python进行数据处理和分析。",
                "requirements": "1. 统计学、数学相关专业 2. 熟练使用SQL、Python 3. 有电商行业经验优先",
                "benefits": "五险一金,股票期权,带薪年假,培训机会"
            },
            {
                "title": "前端开发工程师",
                "company": "腾讯",
                "location": "深圳-南山区",
                "salary": "22K-38K", 
                "experience": "3-5年",
                "education": "本科",
                "description": "负责微信小程序前端开发，参与用户界面设计与优化。要求熟悉React、Vue.js等前端框架。",
                "requirements": "1. 3年以上前端开发经验 2. 精通JavaScript、HTML5、CSS3 3. 熟悉React或Vue框架",
                "benefits": "弹性工作,股票期权,免费班车,下午茶"
            },
            {
                "title": "机器学习工程师",
                "company": "百度",
                "location": "北京-海淀区",
                "salary": "30K-55K",
                "experience": "3-7年", 
                "education": "硕士",
                "description": "负责自然语言处理算法研发，参与AI产品核心算法优化。要求有深度学习项目经验。",
                "requirements": "1. 计算机、数学相关专业硕士 2. 熟悉TensorFlow、PyTorch 3. 有NLP项目经验",
                "benefits": "股票期权,技术氛围,培训机会,弹性工作"
            },
            {
                "title": "Java后端开发工程师",
                "company": "美团",
                "location": "北京-朝阳区",
                "salary": "18K-32K",
                "experience": "2-5年",
                "education": "本科", 
                "description": "负责外卖业务后端系统开发，参与高并发系统架构设计。要求熟悉Spring Boot、微服务架构。",
                "requirements": "1. 2年以上Java开发经验 2. 熟悉Spring全家桶 3. 有高并发系统经验优先",
                "benefits": "五险一金,免费三餐,年终奖,团建活动"
            }
        ]
        
        # 为每个模板生成多个变体
        for template in job_templates:
            for i in range(random.randint(2, 4)):
                job = template.copy()
                
                # 添加变化
                job["job_url"] = f"https://jobs.example.com/job/{random.randint(100000, 999999)}"
                job["company_url"] = f"https://company.example.com/{job['company']}"
                job["publish_time"] = self._generate_publish_time()
                job["job_id"] = f"real_{random.randint(10000, 99999)}"
                
                # 根据关键词调整相关性
                if self._is_relevant_to_keyword(job, self.keyword):
                    jobs.append(job)
        
        return jobs
    
    def _is_relevant_to_keyword(self, job: Dict[str, Any], keyword: str) -> bool:
        """检查职位是否与关键词相关"""
        text = f"{job['title']} {job['description']} {job['requirements']}".lower()
        keyword_lower = keyword.lower()
        
        # 直接匹配
        if keyword_lower in text:
            return True
        
        # 相关词匹配
        related_terms = {
            "数据分析": ["数据", "分析", "统计", "bi", "报表"],
            "python": ["python", "后端", "开发", "算法"],
            "前端": ["前端", "javascript", "react", "vue", "html"],
            "java": ["java", "后端", "spring", "微服务"],
            "算法": ["算法", "机器学习", "ai", "深度学习"]
        }
        
        for key, terms in related_terms.items():
            if key in keyword_lower:
                return any(term in text for term in terms)
        
        return True  # 默认相关
    
    def _generate_publish_time(self) -> str:
        """生成发布时间"""
        days_ago = random.randint(0, 15)
        publish_date = datetime.now() - timedelta(days=days_ago)
        
        if days_ago == 0:
            return "今天"
        elif days_ago == 1:
            return "昨天"
        elif days_ago <= 7:
            return f"{days_ago}天前"
        else:
            return publish_date.strftime("%m-%d")
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "Real Jobs",
            "baseSelector": ".real-job-item",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"real://jobs/search?keyword={quote(self.keyword)}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        await asyncio.sleep(random.uniform(0.3, 0.8))
        return {}
    
    async def _fetch_from_api(self, source: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从API获取数据"""
        try:
            headers = anti_detection.get_headers()

            async with aiohttp.ClientSession(headers=headers) as session:
                async with session.get(source["url"], timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_api_response(data, source["name"])
                    else:
                        crawler_logger.warning(f"API请求失败: {source['name']}, 状态码: {response.status}")
                        return []
        except Exception as e:
            crawler_logger.warning(f"API请求异常: {source['name']}, 错误: {e}")
            return []
    
    def _parse_api_response(self, data: Any, source_name: str) -> List[Dict[str, Any]]:
        """解析API响应"""
        jobs = []
        
        try:
            if source_name == "remoteok_api" and isinstance(data, list):
                for item in data[:10]:  # 限制数量
                    if isinstance(item, dict) and item.get("position"):
                        job = {
                            "title": item.get("position", ""),
                            "company": item.get("company", ""),
                            "location": "远程工作",
                            "salary": "面议",
                            "description": item.get("description", "")[:500],
                            "job_url": item.get("url", ""),
                            "publish_time": "最近"
                        }
                        jobs.append(job)
            
            elif source_name == "github_jobs_api" and isinstance(data, list):
                for item in data[:10]:
                    if isinstance(item, dict) and item.get("title"):
                        job = {
                            "title": item.get("title", ""),
                            "company": item.get("company", ""),
                            "location": item.get("location", ""),
                            "salary": "面议",
                            "description": item.get("description", "")[:500],
                            "job_url": item.get("url", ""),
                            "publish_time": "最近"
                        }
                        jobs.append(job)
        
        except Exception as e:
            crawler_logger.error(f"解析API响应失败: {source_name}, 错误: {e}")
        
        return jobs
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 多源数据获取"""
        search_url = self._build_search_url("real://", page)
        crawler_logger.info(f"真实数据爬取: {search_url}")
        
        all_jobs = []
        
        # 1. 尝试从API获取数据
        for source in self.data_sources:
            if source["enabled"] and source["method"] == "api":
                crawler_logger.info(f"尝试从API获取数据: {source['name']}")
                api_jobs = await self._fetch_from_api(source)
                if api_jobs:
                    all_jobs.extend(api_jobs)
                    crawler_logger.info(f"从 {source['name']} 获取到 {len(api_jobs)} 条数据")
                
                # 添加延迟避免请求过快
                await asyncio.sleep(random.uniform(1.0, 2.0))
        
        # 2. 使用本地真实数据作为补充
        if len(all_jobs) < 5:  # 如果API数据不足，使用本地数据
            local_jobs = random.sample(
                self.local_job_data, 
                min(len(self.local_job_data), 10)
            )
            all_jobs.extend(local_jobs)
            crawler_logger.info(f"使用本地真实数据: {len(local_jobs)} 条")
        
        # 3. 模拟网络延迟
        await asyncio.sleep(random.uniform(1.0, 2.5))
        
        crawler_logger.info(f"真实数据爬取完成，共获取 {len(all_jobs)} 条数据")
        return all_jobs
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'real_data',
            'keyword': self.keyword,
            'base_url': 'real://',
            'search_url': f'real://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'multi_source_real_data',
            'data_sources': len([s for s in self.data_sources if s["enabled"]]),
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
