"""
猎聘网爬虫
Liepin.com crawler
"""

import re
from typing import Dict, Any, List
from urllib.parse import urljoin

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, JsonCssExtractionStrategy

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class LiepinCrawler(BaseCrawler):
    """猎聘网爬虫"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("liepin", keyword)
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取猎聘网默认提取模式"""
        return {
            "name": "Liepin Jobs",
            "baseSelector": ".job-list-item, .job-card-box, [data-selector='job-list-item']",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-title a, .job-name a, h3 a",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name a, .comp-name a, .company-info a",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-area, .work-addr, .job-location",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".text-warning, .job-salary, .salary",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".job-require .text-muted, .job-tags .tag, .experience",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".job-require .text-muted, .job-tags .tag, .education",
                    "type": "text"
                },
                {
                    "name": "job_url",
                    "selector": ".job-title a, .job-name a, h3 a",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "company_url",
                    "selector": ".company-name a, .comp-name a",
                    "type": "attribute", 
                    "attribute": "href"
                },
                {
                    "name": "description",
                    "selector": ".job-content, .job-desc, .job-description",
                    "type": "text"
                }
            ]
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建猎聘网搜索URL"""
        # 猎聘网的分页参数通常是curPage
        if "?" in base_url:
            separator = "&"
        else:
            separator = "?"
        
        return f"{base_url}{separator}curPage={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取猎聘网职位详情页"""
        if not job_url:
            return {}
        
        # 确保URL是完整的
        if job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        
        crawler_logger.debug(f"爬取职位详情: {job_url}")
        
        # 职位详情页的提取模式
        detail_schema = {
            "name": "Liepin Job Detail",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "title",
                    "selector": ".job-apply-title, .job-title h1, h1.job-name",
                    "type": "text"
                },
                {
                    "name": "company",
                    "selector": ".company-name, .comp-summary-name",
                    "type": "text"
                },
                {
                    "name": "salary",
                    "selector": ".job-main-title .text-warning, .salary-text",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".basic-infor .work-addr, .job-location",
                    "type": "text"
                },
                {
                    "name": "experience",
                    "selector": ".basic-infor .experience, .job-qualifications .experience",
                    "type": "text"
                },
                {
                    "name": "education",
                    "selector": ".basic-infor .education, .job-qualifications .education",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": ".job-item .content, .job-description-text, .job-detail-content",
                    "type": "text"
                },
                {
                    "name": "requirements",
                    "selector": ".job-qualifications, .job-require-text",
                    "type": "text"
                },
                {
                    "name": "benefits",
                    "selector": ".job-benefit, .welfare-tab-box",
                    "type": "text"
                }
            ]
        }
        
        # 使用详情页模式爬取
        detail_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(detail_schema),
            js_code=anti_detection.get_js_stealth_code(),
            verbose=True
        )
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                result = await crawler.arun(url=job_url, config=detail_config)
                
                if result.success:
                    detail_data = self._parse_extracted_data(result.extracted_content)
                    if detail_data:
                        return detail_data[0] if isinstance(detail_data, list) else detail_data
                
        except Exception as e:
            crawler_logger.error(f"爬取职位详情失败: {e}")
        
        return {}
    
    def _clean_liepin_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗猎聘网特有的数据格式"""
        cleaned = {}
        
        # 职位标题
        title = raw_data.get('title', '').strip()
        cleaned['title'] = re.sub(r'[\r\n\t]+', ' ', title)
        
        # 公司名称
        company = raw_data.get('company', '').strip()
        cleaned['company'] = re.sub(r'[\r\n\t]+', ' ', company)
        
        # 工作地点
        location = raw_data.get('location', '').strip()
        # 猎聘网地点格式通常是 "城市-区域"
        location = re.sub(r'[\r\n\t]+', ' ', location)
        cleaned['location'] = location
        
        # 薪资处理
        salary = raw_data.get('salary', '').strip()
        # 移除多余的空白字符和特殊符号
        salary = re.sub(r'[\r\n\t]+', ' ', salary)
        salary = re.sub(r'[^\d\-/KkWw万元千·\s]', '', salary)
        cleaned['salary'] = salary
        
        # 工作经验
        experience = raw_data.get('experience', '').strip()
        cleaned['experience'] = re.sub(r'[\r\n\t]+', ' ', experience)
        
        # 学历要求
        education = raw_data.get('education', '').strip()
        cleaned['education'] = re.sub(r'[\r\n\t]+', ' ', education)
        
        # 职位描述
        description = raw_data.get('description', '').strip()
        description = re.sub(r'[\r\n\t]+', ' ', description)
        cleaned['description'] = description
        
        # 职位要求
        requirements = raw_data.get('requirements', '').strip()
        requirements = re.sub(r'[\r\n\t]+', ' ', requirements)
        cleaned['requirements'] = requirements
        
        # 福利待遇
        benefits = raw_data.get('benefits', '').strip()
        benefits = re.sub(r'[\r\n\t]+', ' ', benefits)
        cleaned['benefits'] = benefits
        
        # URL处理
        job_url = raw_data.get('job_url', '')
        if job_url and job_url.startswith('/'):
            job_url = urljoin(self.website_config.base_url, job_url)
        cleaned['job_url'] = job_url
        
        return cleaned
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """重写搜索页面爬取，添加猎聘网特有的处理"""
        jobs_data = await super().crawl_search_page(page)
        
        # 对猎聘网数据进行特殊清洗
        cleaned_jobs = []
        for job_data in jobs_data:
            cleaned_job = self._clean_liepin_data(job_data)
            if cleaned_job.get('title') and cleaned_job.get('company'):
                cleaned_jobs.append(cleaned_job)
        
        return cleaned_jobs
    
    def _extract_page_info(self, html: str) -> Dict[str, Any]:
        """提取页面信息（总页数、当前页等）"""
        page_info = {
            'current_page': 1,
            'total_pages': 1,
            'total_jobs': 0
        }
        
        try:
            # 尝试从HTML中提取分页信息
            # 猎聘网的分页信息通常在特定的元素中
            import re
            
            # 查找总页数
            total_pages_match = re.search(r'共(\d+)页', html)
            if total_pages_match:
                page_info['total_pages'] = int(total_pages_match.group(1))
            
            # 查找总职位数
            total_jobs_match = re.search(r'共找到(\d+)个职位', html)
            if total_jobs_match:
                page_info['total_jobs'] = int(total_jobs_match.group(1))
                
        except Exception as e:
            crawler_logger.debug(f"提取页面信息失败: {e}")
        
        return page_info
