"""
专业猎聘网爬虫 - 获取全部真实招聘信息
Professional Liepin Crawler - Get all real recruitment information
"""

import asyncio
import aiohttp
import json
import random
import time
from typing import Dict, Any, List
from urllib.parse import quote, urljoin
from datetime import datetime
import re

from .base_crawler import BaseCrawler
from ..utils import crawler_logger, anti_detection


class ProfessionalLiepinCrawler(BaseCrawler):
    """专业猎聘网爬虫 - 无遗漏数据获取"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        super().__init__("professional_liepin", keyword)
        
        # 猎聘网API端点
        self.api_endpoints = {
            "search": "https://api-c.liepin.com/api/com.liepin.searchfront4c.pc-search-job",
            "detail": "https://www.liepin.com/job/",
            "company": "https://www.liepin.com/company/"
        }
        
        # 城市代码映射
        self.city_codes = {
            "全国": "000",
            "北京": "010", "上海": "020", "广州": "050", "深圳": "060",
            "杭州": "070", "成都": "280", "武汉": "170", "西安": "200",
            "南京": "080", "苏州": "090", "天津": "030", "重庆": "040"
        }
        
        # 行业代码
        self.industry_codes = {
            "互联网": "010", "计算机软件": "020", "IT服务": "030",
            "电子技术": "040", "通信": "050", "人工智能": "060"
        }
        
        # 请求头配置
        self.headers_pool = [
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Referer": "https://www.liepin.com/",
                "Origin": "https://www.liepin.com",
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-site"
            }
        ]
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认提取模式"""
        return {
            "name": "Professional Liepin Jobs",
            "baseSelector": ".professional-liepin-job",
            "fields": []
        }
    
    def _build_search_url(self, base_url: str, page: int) -> str:
        """构建搜索URL"""
        return f"professional_liepin://search?keyword={quote(self.keyword)}&page={page}"
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, Any]:
        """爬取职位详情页"""
        await asyncio.sleep(random.uniform(0.5, 1.5))
        return {}
    
    def _build_search_params(self, keyword: str, page: int, city_code: str = "000") -> Dict[str, Any]:
        """构建搜索参数"""
        return {
            "data": json.dumps({
                "mainSearchPcConditionForm": {
                    "city": city_code,
                    "dq": city_code,
                    "pubTime": "",
                    "currentPage": page,
                    "pageSize": 40,  # 每页40条
                    "key": keyword,
                    "suggestTag": "",
                    "workYearCode": "0",
                    "compId": "",
                    "compName": "",
                    "compTag": "",
                    "industry": "",
                    "salary": "",
                    "jobKind": "",
                    "compScale": "",
                    "compKind": "",
                    "compStage": "",
                    "eduLevel": ""
                },
                "passThroughForm": {
                    "scene": "input",
                    "skId": "",
                    "fkId": "",
                    "ckId": ""
                }
            })
        }
    
    async def _fetch_liepin_jobs(self, keyword: str, page: int = 1, city_code: str = "000") -> List[Dict[str, Any]]:
        """获取猎聘网职位数据"""
        jobs = []
        
        try:
            headers = random.choice(self.headers_pool).copy()
            headers.update(anti_detection.get_headers())
            
            # 构建请求参数
            params = self._build_search_params(keyword, page, city_code)
            
            async with aiohttp.ClientSession(headers=headers) as session:
                # 首先访问主页获取cookies
                await session.get("https://www.liepin.com/")
                await asyncio.sleep(random.uniform(1, 2))
                
                # 发送搜索请求
                async with session.post(self.api_endpoints["search"], data=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get("flag") == 1:
                            job_list = result.get("data", {}).get("data", {}).get("jobCardList", [])
                            
                            for job_data in job_list:
                                job = self._parse_liepin_job(job_data)
                                if job:
                                    jobs.append(job)
                            
                            crawler_logger.info(f"猎聘网第{page}页获取到 {len(jobs)} 条职位")
                        else:
                            crawler_logger.warning(f"猎聘网API返回错误: {result.get('msg', '未知错误')}")
                    else:
                        crawler_logger.error(f"猎聘网请求失败，状态码: {response.status}")
                
        except Exception as e:
            crawler_logger.error(f"猎聘网爬取异常: {e}")
        
        return jobs
    
    def _parse_liepin_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析猎聘网职位数据"""
        try:
            job = {
                "title": job_data.get("job", {}).get("title", ""),
                "company": job_data.get("comp", {}).get("compName", ""),
                "location": f"{job_data.get('job', {}).get('dq', '')}-{job_data.get('job', {}).get('workAddress', '')}",
                "salary": job_data.get("job", {}).get("salary", ""),
                "experience": job_data.get("job", {}).get("requireWorkYears", ""),
                "education": job_data.get("job", {}).get("requireEduLevel", ""),
                "description": job_data.get("job", {}).get("jobDesc", ""),
                "requirements": job_data.get("job", {}).get("requirement", ""),
                "benefits": ", ".join(job_data.get("job", {}).get("labels", [])),
                "job_url": f"https://www.liepin.com/job/{job_data.get('job', {}).get('jobId', '')}.shtml",
                "company_url": f"https://www.liepin.com/company/{job_data.get('comp', {}).get('compId', '')}/",
                "publish_time": job_data.get("job", {}).get("refreshTime", ""),
                "job_type": "全职",
                "company_size": job_data.get("comp", {}).get("compScale", ""),
                "company_industry": job_data.get("comp", {}).get("compIndustry", ""),
                "source_website": "liepin"
            }
            
            return job
            
        except Exception as e:
            crawler_logger.error(f"解析猎聘网职位数据失败: {e}")
            return None
    
    async def crawl_all_cities(self, keyword: str, max_pages_per_city: int = 50) -> List[Dict[str, Any]]:
        """爬取所有城市的职位数据"""
        all_jobs = []
        
        for city_name, city_code in self.city_codes.items():
            crawler_logger.info(f"开始爬取猎聘网 {city_name} 的职位数据")
            
            page = 1
            consecutive_empty_pages = 0
            
            while page <= max_pages_per_city and consecutive_empty_pages < 3:
                try:
                    jobs = await self._fetch_liepin_jobs(keyword, page, city_code)
                    
                    if jobs:
                        all_jobs.extend(jobs)
                        consecutive_empty_pages = 0
                        crawler_logger.info(f"猎聘网 {city_name} 第{page}页: {len(jobs)}条职位")
                    else:
                        consecutive_empty_pages += 1
                        crawler_logger.warning(f"猎聘网 {city_name} 第{page}页: 无数据")
                    
                    page += 1
                    
                    # 随机延迟
                    await asyncio.sleep(random.uniform(2, 4))
                    
                except Exception as e:
                    crawler_logger.error(f"猎聘网 {city_name} 第{page}页爬取失败: {e}")
                    consecutive_empty_pages += 1
                    page += 1
            
            crawler_logger.info(f"猎聘网 {city_name} 爬取完成，共获取 {len([j for j in all_jobs if city_name in j.get('location', '')])} 条职位")
            
            # 城市间延迟
            await asyncio.sleep(random.uniform(3, 6))
        
        return all_jobs
    
    async def crawl_search_page(self, page: int = 1) -> List[Dict[str, Any]]:
        """爬取搜索页面 - 全面覆盖"""
        search_url = self._build_search_url("professional_liepin://", page)
        crawler_logger.info(f"专业猎聘网爬虫启动: {search_url}")
        
        # 如果是第一页，进行全面爬取
        if page == 1:
            all_jobs = await self.crawl_all_cities(self.keyword, max_pages_per_city=20)
            crawler_logger.info(f"猎聘网全面爬取完成，共获取 {len(all_jobs)} 条职位")
            return all_jobs
        else:
            # 单页爬取
            jobs = await self._fetch_liepin_jobs(self.keyword, page)
            return jobs
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        return {
            'website': 'professional_liepin',
            'keyword': self.keyword,
            'base_url': 'https://www.liepin.com',
            'search_url': f'professional_liepin://search?keyword={self.keyword}',
            'enabled': True,
            'type': 'professional_comprehensive_crawler',
            'cities_covered': len(self.city_codes),
            'api_endpoints': len(self.api_endpoints),
            'data_cleaner_stats': self.data_cleaner.get_statistics()
        }
