"""
主程序入口
Main program entry point
"""

import asyncio
import time
from typing import List, Dict, Any
from pathlib import Path

from .crawlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>raw<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>raw<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RealCrawler, EnhancedWebCrawler
from .data_processing import DataCleaner, DataStorage, JobPosition
from .utils import logger, config


class CrawlerManager:
    """爬虫管理器"""

    def __init__(self, keyword: str = "计算机科学与技术"):
        self.keyword = keyword
        self.crawlers = {}
        self.data_storage = DataStorage()
        self.statistics = {
            'total_jobs': 0,
            'jobs_by_site': {},
            'start_time': None,
            'end_time': None,
            'duration': 0,
            'keyword': keyword
        }

        # 初始化爬虫
        self._init_crawlers()
    
    def _init_crawlers(self):
        """初始化爬虫实例"""
        crawler_classes = {
            'liepin': <PERSON><PERSON><PERSON><PERSON><PERSON>,
            'job51': <PERSON><PERSON><PERSON><PERSON><PERSON>,
            'job58': <PERSON>5<PERSON><PERSON><PERSON><PERSON>,
            'boss': <PERSON><PERSON><PERSON><PERSON>,
            'zhilian': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            'demo': <PERSON><PERSON><PERSON><PERSON><PERSON>,
            'real': RealCrawler,
            'enhanced': EnhancedWebCrawler
        }

        for site_name, crawler_class in crawler_classes.items():
            site_config = config.websites.get(site_name)
            if site_config and site_config.enabled:
                try:
                    self.crawlers[site_name] = crawler_class(self.keyword)
                    logger.info(f"初始化爬虫: {site_name}, 关键词: {self.keyword}")
                except Exception as e:
                    logger.error(f"初始化爬虫失败 {site_name}: {e}")
    
    async def crawl_single_site(self, site_name: str, max_pages: int = None) -> List[JobPosition]:
        """爬取单个网站"""
        if site_name not in self.crawlers:
            logger.error(f"爬虫不存在: {site_name}")
            return []
        
        crawler = self.crawlers[site_name]
        max_pages = max_pages or config.config_data.get('crawl_settings', {}).get('max_pages_per_site', 5)
        
        logger.info(f"开始爬取网站: {site_name}, 最大页数: {max_pages}")
        
        try:
            jobs = await crawler.crawl_multiple_pages(max_pages)
            self.statistics['jobs_by_site'][site_name] = len(jobs)
            logger.info(f"完成爬取 {site_name}: {len(jobs)} 条职位")
            return jobs
            
        except Exception as e:
            logger.error(f"爬取网站失败 {site_name}: {e}")
            self.statistics['jobs_by_site'][site_name] = 0
            return []
    
    async def crawl_all_sites(self, max_pages: int = None) -> List[JobPosition]:
        """爬取所有启用的网站"""
        self.statistics['start_time'] = time.time()
        logger.info("开始爬取所有网站")
        
        all_jobs = []
        
        # 获取并发设置
        concurrent_sites = config.config_data.get('crawl_settings', {}).get('concurrent_sites', 1)
        
        if concurrent_sites > 1:
            # 并发爬取
            tasks = []
            for site_name in self.crawlers.keys():
                task = self.crawl_single_site(site_name, max_pages)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    site_name = list(self.crawlers.keys())[i]
                    logger.error(f"并发爬取失败 {site_name}: {result}")
                else:
                    all_jobs.extend(result)
        else:
            # 顺序爬取
            for site_name in self.crawlers.keys():
                jobs = await self.crawl_single_site(site_name, max_pages)
                all_jobs.extend(jobs)
                
                # 网站间延迟
                if site_name != list(self.crawlers.keys())[-1]:
                    await asyncio.sleep(config.crawler.request_delay)
        
        self.statistics['end_time'] = time.time()
        self.statistics['duration'] = self.statistics['end_time'] - self.statistics['start_time']
        self.statistics['total_jobs'] = len(all_jobs)
        
        logger.info(f"完成所有网站爬取: 总计 {len(all_jobs)} 条职位，耗时 {self.statistics['duration']:.2f} 秒")
        
        return all_jobs
    
    def save_jobs(self, jobs: List[JobPosition], format_type: str = None) -> str:
        """保存职位数据"""
        if not jobs:
            logger.warning("没有职位数据需要保存")
            return ""
        
        try:
            filepath = self.data_storage.save_jobs(jobs, format_type)
            logger.info(f"职位数据已保存: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"保存职位数据失败: {e}")
            return ""
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬取统计信息"""
        stats = self.statistics.copy()
        
        # 添加爬虫统计信息
        stats['crawler_stats'] = {}
        for site_name, crawler in self.crawlers.items():
            stats['crawler_stats'][site_name] = crawler.get_statistics()
        
        # 添加存储统计信息
        stats['storage_stats'] = self.data_storage.get_statistics()
        
        return stats
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        
        print("\n" + "="*60)
        print("爬取统计报告")
        print("="*60)

        print(f"搜索关键词: {stats.get('keyword', 'N/A')}")
        print(f"总爬取时间: {stats['duration']:.2f} 秒")
        print(f"总职位数量: {stats['total_jobs']} 条")
        
        print("\n各网站爬取结果:")
        for site_name, count in stats['jobs_by_site'].items():
            print(f"  {site_name}: {count} 条")
        
        print("\n数据存储信息:")
        storage_stats = stats['storage_stats']
        print(f"  输出格式: {storage_stats.get('output_format', 'N/A')}")
        print(f"  输出目录: {storage_stats.get('output_directory', 'N/A')}")
        print(f"  文件数量: {storage_stats.get('files_count', 0)}")
        
        if 'by_website' in storage_stats:
            print("\n数据库统计:")
            for site, count in storage_stats['by_website'].items():
                print(f"  {site}: {count} 条")
        
        print("="*60)


async def main(keyword: str = "计算机科学与技术"):
    """主函数"""
    logger.info(f"启动工业职位需求爬虫系统，搜索关键词: {keyword}")

    # 创建必要的目录
    Path("data/output").mkdir(parents=True, exist_ok=True)
    Path("logs").mkdir(parents=True, exist_ok=True)

    # 创建爬虫管理器
    crawler_manager = CrawlerManager(keyword)
    
    try:
        # 爬取所有网站
        jobs = await crawler_manager.crawl_all_sites()
        
        if jobs:
            # 保存数据
            output_format = config.data_storage.output_format
            filepath = crawler_manager.save_jobs(jobs, output_format)
            
            # 如果配置了多种输出格式，也保存其他格式
            if output_format != "json":
                crawler_manager.save_jobs(jobs, "json")
            if output_format != "csv":
                crawler_manager.save_jobs(jobs, "csv")
            
            # 导出Excel文件
            try:
                excel_path = crawler_manager.data_storage.export_to_excel(jobs)
                logger.info(f"Excel文件已导出: {excel_path}")
            except Exception as e:
                logger.warning(f"导出Excel文件失败: {e}")
        
        # 打印统计信息
        crawler_manager.print_statistics()
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程发生错误: {e}")
        raise
    finally:
        logger.info("爬虫系统结束")


if __name__ == "__main__":
    asyncio.run(main())
